// Pusher Beams Service Worker 스크립트 import
/* global importScripts */
try {
	// Tauri 환경에서는 외부 스크립트 로드가 제한될 수 있음
	importScripts('https://js.pusher.com/beams/service-worker.js');
	console.log('[Service Worker] Pusher Beams 스크립트 로드 성공');
} catch (error) {
	console.warn('[Service Worker] Pusher Beams 스크립트 로드 실패:', error);
	console.log('[Service Worker] 기본 푸시 알림 처리로 폴백');
}

// 커스텀 푸시 알림 수신 처리 로직
self.addEventListener('push', function (event) {
	console.log('[Service Worker] Push 이벤트 수신:', event);

	if (!event.data) {
		console.log('[Service Worker] Push 이벤트에 데이터가 없습니다.');
		return;
	}

	try {
		const data = event.data.json();
		console.log('[Service Worker] Push 데이터:', data);

		// 우선순위별 알림 설정
		const priority = data.priority || 'normal';
		const options = {
			body: data.body || data.message || '새로운 알림이 있습니다.',
			icon: '/favicon.png',
			badge: '/favicon.png',
			tag: data.tag || `cnsprowms-${priority}-${data.id || Date.now()}`,
			data: {
				notificationId: data.id || Date.now().toString(),
				timestamp: Date.now(),
				priority: priority,
				category: data.category,
				deletable: data.deletable || false,
				...data
			},
			requireInteraction: priority === 'high' || priority === 'urgent',
			silent: priority === 'low',
			// 우선순위별 시각적 구분
			image: data.imageUrl || data.image_url,
			vibrate:
				priority === 'urgent' ? [200, 100, 200] : priority === 'high' ? [100, 50, 100] : undefined
		};

		// 알림 제목 설정
		const title = data.title || 'CNSPROWMS';

		// 메인 스레드로 알림 데이터 전달 (IndexedDB 저장용)
		const notificationData = {
			id: data.id || Date.now().toString(),
			title: title,
			body: data.body || data.message || '새로운 알림이 있습니다.',
			priority: priority,
			category: data.category,
			imageUrl: data.imageUrl || data.image_url,
			expireAt: data.expireAt,
			deletable: data.deletable || false,
			data: data
		};

		// 클라이언트에게 알림 데이터 전달
		event.waitUntil(
			Promise.all([
				// 브라우저 알림 표시
				self.registration.showNotification(title, options),
				// 메인 스레드로 데이터 전달
				self.clients.matchAll().then((clients) => {
					clients.forEach((client) => {
						client.postMessage({
							type: 'PUSH_NOTIFICATION',
							notification: notificationData
						});
					});
				})
			])
		);
	} catch (error) {
		console.error('[Service Worker] Push 데이터 파싱 오류:', error);

		// 파싱 실패 시 기본 알림 표시
		event.waitUntil(
			self.registration.showNotification('CNSPROWMS', {
				body: '새로운 알림이 있습니다.',
				icon: '/favicon.png',
				tag: 'cnsprowms-fallback'
			})
		);
	}
});

// 알림 클릭 처리
self.addEventListener('notificationclick', function (event) {
	console.log('[Service Worker] 알림 클릭:', event);

	event.notification.close();

	const data = event.notification.data || {};
	const url = '/'; // 기본 홈페이지로 이동

	console.log('[Service Worker] 알림 클릭 - 이동할 URL:', url);

	event.waitUntil(
		self.clients
			.matchAll({ type: 'window', includeUncontrolled: true })
			.then(function (clientList) {
				// 이미 열린 창이 있는지 확인
				for (let i = 0; i < clientList.length; i++) {
					const client = clientList[i];
					if (client.url.includes(self.location.origin) && 'focus' in client) {
						// 기존 창을 포커스하고 URL 이동
						return client.focus().then(() => {
							if ('navigate' in client) {
								return client.navigate(url);
							}
						});
					}
				}

				// 새 창 열기
				if (self.clients.openWindow) {
					return self.clients.openWindow(url);
				}
			})
			.catch(function (error) {
				console.error('[Service Worker] 알림 클릭 처리 오류:', error);
			})
	);
});

// 알림 닫기 처리
self.addEventListener('notificationclose', function (event) {
	console.log('[Service Worker] 알림 닫힘:', event);

	// 필요시 알림 닫힘 이벤트 처리 로직 추가
	const data = event.notification.data || {};

	// 통계나 로깅을 위한 처리 (선택사항)
	if (data.notificationId) {
		console.log('[Service Worker] 알림 ID', data.notificationId, '닫힘');
	}
});

// Service Worker 설치 이벤트
self.addEventListener('install', function (event) {
	console.log('[Service Worker] 설치됨');
	self.skipWaiting();
});

// Service Worker 활성화 이벤트
self.addEventListener('activate', function (event) {
	console.log('[Service Worker] 활성화됨');
	event.waitUntil(self.clients.claim());
});

// 백그라운드 동기화 (선택사항)
self.addEventListener('sync', function (event) {
	console.log('[Service Worker] 백그라운드 동기화:', event.tag);

	if (event.tag === 'background-sync') {
		event.waitUntil(
			// 백그라운드에서 수행할 작업 (예: 오프라인 데이터 동기화)
			Promise.resolve()
		);
	}
});
