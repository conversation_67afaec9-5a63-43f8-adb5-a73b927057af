{"$schema": "../gen/schemas/desktop-schema.json", "identifier": "default", "description": "Capability for the main window on desktop platforms", "windows": ["main", "Print"], "platforms": ["macOS", "windows", "linux"], "permissions": ["core:default", "opener:default", {"identifier": "opener:allow-open-path", "allow": [{"path": "$DOWNLOAD/**"}]}, "dialog:default", "fs:default", {"identifier": "fs:allow-exists", "allow": [{"path": "$DOCUMENT/CornerstoneWMS/**"}, {"path": "$DOWNLOAD/**"}]}, {"identifier": "fs:allow-document-write", "allow": [{"path": "$DOCUMENT/CornerstoneWMS/**"}, {"path": "$DOWNLOAD/**"}]}, {"identifier": "fs:allow-read-file", "allow": [{"path": "$DOCUMENT/CornerstoneWMS/**"}, {"path": "$DOWNLOAD/**"}]}, {"identifier": "fs:allow-remove", "allow": [{"path": "$DOCUMENT/CornerstoneWMS/**"}, {"path": "$DOWNLOAD/**"}]}, "updater:default", "process:default", "printer:default", "notification:default", {"identifier": "notification:allow-request-permission"}, {"identifier": "notification:allow-show"}, {"identifier": "notification:allow-is-permission-granted"}, "core:window:allow-start-dragging", "core:webview:allow-create-webview-window", "core:webview:allow-webview-close", "core:webview:allow-internal-toggle-devtools", "core:window:allow-close", "core:window:allow-minimize", "core:window:allow-maximize", "core:window:allow-unmaximize", "core:window:allow-show", "core:window:allow-hide", "core:window:allow-set-focus", "store:default", "os:default"], "allow": {"windows": ["^coupang-.*"]}}