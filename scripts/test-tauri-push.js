#!/usr/bin/env node

/**
 * Tauri 푸시 알림 테스트 및 검증 스크립트
 * CSP, IPC 통신, 환경 감지 등을 종합적으로 테스트
 */

import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log('🔔 Tauri 푸시 알림 종합 테스트 시작\n');

// 1. 환경 변수 확인
console.log('1. 환경 변수 확인...');
const envPath = path.join(__dirname, '..', '.env');
if (fs.existsSync(envPath)) {
    const envContent = fs.readFileSync(envPath, 'utf8');
    const requiredVars = [
        'VITE_PUSHER_BEAMS_INSTANCE_ID',
        'VITE_FIREBASE_SENDER_ID',
        'VITE_FIREBASE_PROJECT_ID',
        'VITE_FIREBASE_API_KEY'
    ];
    
    const missingVars = requiredVars.filter(varName => !envContent.includes(varName));
    
    if (missingVars.length > 0) {
        console.error('❌ 누락된 환경 변수:', missingVars.join(', '));
        process.exit(1);
    }
    
    console.log('✅ 모든 필수 환경 변수가 설정되어 있습니다.');
} else {
    console.error('❌ .env 파일을 찾을 수 없습니다.');
    process.exit(1);
}

// 2. Tauri 설정 확인
console.log('\n2. Tauri 설정 확인...');
const tauriConfigPath = path.join(__dirname, '..', 'src-tauri', 'tauri.conf.json');
if (fs.existsSync(tauriConfigPath)) {
    const tauriConfig = JSON.parse(fs.readFileSync(tauriConfigPath, 'utf8'));
    
    // Firebase Sender ID 일치 확인
    const envContent = fs.readFileSync(envPath, 'utf8');
    const envSenderId = envContent.match(/VITE_FIREBASE_SENDER_ID=(\d+)/)?.[1];
    const tauriSenderId = tauriConfig.plugins?.['remote-push']?.senderId;
    
    if (envSenderId && tauriSenderId && envSenderId === tauriSenderId) {
        console.log('✅ Firebase Sender ID가 일치합니다.');
    } else {
        console.warn('⚠️  Firebase Sender ID 불일치:', { env: envSenderId, tauri: tauriSenderId });
    }
    
    // CSP 확인
    if (tauriConfig.app && tauriConfig.app.security && tauriConfig.app.security.csp) {
        const csp = tauriConfig.app.security.csp;
        console.log('✅ CSP가 설정되어 있습니다.');
        
        // Tauri IPC 프로토콜 확인
        if (csp.includes('ipc:') && csp.includes('http://ipc.localhost')) {
            console.log('✅ Tauri IPC 프로토콜이 CSP에 포함되어 있습니다.');
        } else {
            console.warn('⚠️  CSP에 Tauri IPC 프로토콜이 누락되었을 수 있습니다.');
            console.log('현재 CSP:', csp);
        }
        
        // 필수 CSP 지시어 확인
        const requiredDirectives = ['tauri:', 'ipc:', 'http://ipc.localhost'];
        const missingDirectives = requiredDirectives.filter(directive => !csp.includes(directive));
        
        if (missingDirectives.length > 0) {
            console.warn('⚠️  누락된 CSP 지시어:', missingDirectives.join(', '));
        }
    } else {
        console.error('❌ CSP가 설정되지 않았습니다.');
    }
    
    // 권한 확인
    const capabilities = [
        path.join(__dirname, '..', 'src-tauri', 'capabilities', 'default.json'),
        path.join(__dirname, '..', 'src-tauri', 'capabilities', 'desktop.json')
    ];
    
    let hasNotificationPermission = false;
    
    capabilities.forEach(capPath => {
        if (fs.existsSync(capPath)) {
            const capConfig = JSON.parse(fs.readFileSync(capPath, 'utf8'));
            if (capConfig.permissions && capConfig.permissions.includes('notification:default')) {
                hasNotificationPermission = true;
            }
        }
    });
    
    if (hasNotificationPermission) {
        console.log('✅ Tauri 알림 권한이 설정되어 있습니다.');
    } else {
        console.warn('⚠️  Tauri 알림 권한이 설정되지 않았을 수 있습니다.');
    }
    
} else {
    console.error('❌ tauri.conf.json 파일을 찾을 수 없습니다.');
    process.exit(1);
}

// 3. 소스 코드 확인
console.log('\n3. 소스 코드 확인...');

// 환경 감지 로직 확인
const tauriConfigFile = path.join(__dirname, '..', 'src', 'lib', 'utils', 'tauri-push-config.ts');
if (fs.existsSync(tauriConfigFile)) {
    const content = fs.readFileSync(tauriConfigFile, 'utf8');
    
    if (content.includes('isTauriEnvironment') && content.includes('window.__TAURI__')) {
        console.log('✅ Tauri 환경 감지 로직이 구현되어 있습니다.');
    } else {
        console.warn('⚠️  Tauri 환경 감지 로직이 누락되었을 수 있습니다.');
    }
    
    if (content.includes('getPushNotificationStrategy')) {
        console.log('✅ 푸시 알림 전략 선택 로직이 구현되어 있습니다.');
    } else {
        console.warn('⚠️  푸시 알림 전략 선택 로직이 누락되었을 수 있습니다.');
    }
} else {
    console.warn('⚠️  tauri-push-config.ts 파일을 찾을 수 없습니다.');
}

// 4. 의존성 확인
console.log('\n4. 의존성 확인...');
try {
    const packageJsonPath = path.join(__dirname, '..', 'package.json');
    const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
    
    const requiredDeps = [
        '@tauri-apps/plugin-notification',
        '@pusher/push-notifications-web'
    ];
    
    const allDeps = { ...packageJson.dependencies, ...packageJson.devDependencies };
    const missingDeps = requiredDeps.filter(dep => !allDeps[dep]);
    
    if (missingDeps.length > 0) {
        console.error('❌ 누락된 의존성:', missingDeps.join(', '));
        console.log('다음 명령어로 설치하세요:');
        console.log(`npm install ${missingDeps.join(' ')}`);
        process.exit(1);
    }
    
    console.log('✅ 모든 필수 의존성이 설치되어 있습니다.');
} catch (error) {
    console.error('❌ package.json 확인 실패:', error.message);
    process.exit(1);
}

// 5. 빌드 테스트
console.log('\n5. 빌드 테스트...');
try {
    console.log('프론트엔드 빌드 중...');
    execSync('npm run build', { stdio: 'pipe' });
    console.log('✅ 프론트엔드 빌드 성공');
    
} catch (error) {
    console.error('❌ 프론트엔드 빌드 실패');
    console.log('빌드 로그를 확인하세요: npm run build');
}

// 6. 테스트 완료 및 가이드
console.log('\n🎉 테스트 완료!');
console.log('\n📋 다음 단계:');
console.log('1. npm run tauri dev - 개발 서버 시작');
console.log('2. npm run tauri build --debug - 디버그 빌드');
console.log('3. 빌드된 앱에서 알림 테스트');

console.log('\n🔍 확인 사항:');
console.log('- 앱 시작 시 "[TauriPushConfig] Tauri 환경: true" 로그 확인');
console.log('- 알림 권한 요청 다이얼로그 표시 확인');
console.log('- Windows 시스템 알림 표시 확인');
console.log('- CSP 위반 오류가 없는지 확인');

console.log('\n🐛 디버깅 팁:');
console.log('- F12 개발자 도구에서 콘솔 로그 확인');
console.log('- "[TauriPushConfig]" 로그로 환경 감지 상태 확인');
console.log('- CSP 오류 발생 시 tauri.conf.json의 CSP 설정 확인');
console.log('- IPC 통신 실패 시 Tauri 플러그인 설치 상태 확인');
