<script lang="ts">
	import { onMount } from 'svelte';
	import { page } from '$app/state';
	import type { LoginCredentials } from '$lib/types/auth';
	import { goto } from '$app/navigation';

	import { authActions, authState } from '$lib/stores/authStore';
	import { isUser } from '$lib/User';
	import { executeMessage } from '$lib/Functions';
	import { validateCredentials, redirectAfterLogin, redirectToLogin } from '$lib/utils/authHelpers';

	import { setTheme, themes } from '$lib/Themes';

	import PasswordToggleButton from '$components/Button/PasswordToggleButton.svelte';

	import Icon from 'svelte-awesome';
	import { faKey } from '@fortawesome/free-solid-svg-icons/faKey';
	import { faPalette } from '@fortawesome/free-solid-svg-icons/faPalette';

	// 이미 로그인된 사용자는 대시보드로 리다이렉트
	if (isUser()) {
		redirectAfterLogin(page.url.searchParams, '/dashboard');
	}

	// Svelte 5 runes를 사용한 상태 관리
	let username: string = $state('');
	let password: string = $state('');
	let logoutMessage: string = $state('');

	const isDevelopment = import.meta.env.VITE_NODE_ENV === 'development';

	// JWT 인증 방식으로 변경된 로그인 처리 함수
	async function handleLogin(event: SubmitEvent) {
		event.preventDefault();

		// 입력 유효성 검사
		const credentials: LoginCredentials = {
			username,
			password
		};

		const validation = validateCredentials(credentials);
		if (!validation.isValid) {
			await executeMessage(validation.errors.join('\n'), 'error');
			return;
		}

		try {
			// AuthStore를 통한 JWT 로그인 처리
			await authActions.login(credentials);

			// 로그인 성공 시 적절한 페이지로 리다이렉트
			await redirectAfterLogin(page.url.searchParams, '/dashboard');
		} catch (error: any) {
			// AuthStore에서 처리된 에러를 사용자에게 표시
			const errorMessage = authActions.getErrorMessage(error);
			await executeMessage(errorMessage, 'error');

			if (isDevelopment) {
				console.error('[Login] JWT 로그인 실패:', error);
			}
		}
	}

	let showPassword = $state(false);
	function togglePassword() {
		showPassword = !showPassword;
	}

	const defaultTheme = 'light';
	let currentTheme: string = $state(defaultTheme);

	onMount(async () => {
		// 테마 설정 초기화
		if (typeof window !== 'undefined') {
			const theme = window.localStorage.getItem('theme');
			if (theme && themes.includes(theme)) {
				document.documentElement.setAttribute('data-theme', theme);
				setTheme(theme);
				currentTheme = theme;
			} else {
				setTheme(defaultTheme);
				currentTheme = defaultTheme;
			}

			// 로그아웃 사유 확인 및 표시
			try {
				const reason = sessionStorage.getItem('logout_reason');
				if (reason) {
					logoutMessage = reason;
					sessionStorage.removeItem('logout_reason'); // 한 번 표시 후 삭제
				}
			} catch (error) {
				// 세션 스토리지 접근 실패 시 무시
			}
		}

		// AuthStore 초기화 (이미 로그인된 경우 체크)
		try {
			await authActions.initialize();

			// 초기화 후 인증 상태 확인
			if ($authState.isAuthenticated) {
				await goto('/dashboard');
			}
		} catch (error) {
			if (isDevelopment) {
				console.error('[Login] AuthStore 초기화 실패:', error);
			}
		}
	});
</script>

<!-- component -->
<div>
	<div class="relative flex flex-col justify-center h-screen overflow-hidden">
		<div
			class="w-full p-6 m-auto bg-base-100 text-base-content rounded-md shadow-md ring-2 ring-base-content/20 lg:max-w-lg"
		>
			<div class="flex justify-center">
				<div class="text-3xl font-semibold text-center">CornerStone Project WMS</div>
				<div class="dropdown dropdown-hover dropdown-end">
					<button class="btn btn-ghost" tabindex="0">
						<Icon data={faPalette} />
					</button>

					<ul
						class="dropdown-content menu z-[1] p-2 bg-base-100 shadow rounded-box w-96 h-96 border border-base-300"
					>
						{#each themes as theme}
							<li>
								<button
									class="justify-between"
									onclick={() => {
										setTheme(theme);
										currentTheme = theme;
									}}
								>
									{theme}
									{#if theme === currentTheme}
										<span class="badge badge-primary badge-xs">현재</span>
									{/if}
								</button>
							</li>
						{/each}
					</ul>
				</div>
			</div>

			<form onsubmit={handleLogin}>
				<div>
					<label class="label" for="username">
						<span class="label-text">아이디</span>
					</label>
					<input
						bind:value={username}
						class="w-full input input-bordered"
						id="username"
						name="username"
						required
						type="text"
					/>
				</div>
				<div>
					<label class="label" for="password">
						<span class="label-text">비밀번호</span>
					</label>
					<div class="relative">
						<input
							bind:value={password}
							class="w-full input input-bordered"
							id="password"
							name="password"
							required
							type="{showPassword ? 'text' : 'password'}"
						/>
						<PasswordToggleButton
							isVisible={showPassword}
							onToggle={togglePassword}
						/>
					</div>
				</div>

				<div class="divider"></div>

				<div>
					<button
						class="btn btn-block"
						class:loading={$authState.isLoading}
						disabled={$authState.isLoading}
					>
						{#if $authState.isLoading}
							<span class="loading loading-spinner loading-sm"></span>
							로그인 중...
						{:else}
							<Icon class="mr-2" data={faKey} />
							로그인
						{/if}
					</button>
				</div>

				<!-- 로그아웃 사유 메시지 표시 -->
				{#if logoutMessage}
					<div class="alert alert-info mt-4">
						<svg
							xmlns="http://www.w3.org/2000/svg"
							class="stroke-current shrink-0 h-6 w-6"
							fill="none"
							viewBox="0 0 24 24"
						>
							<path
								stroke-linecap="round"
								stroke-linejoin="round"
								stroke-width="2"
								d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
							/>
						</svg>
						<span>{logoutMessage}</span>
						<button class="btn btn-sm btn-ghost" onclick={() => (logoutMessage = '')}>
							닫기
						</button>
					</div>
				{/if}

				<!-- 에러 메시지 표시 -->
				{#if $authState.error}
					<div class="alert alert-error mt-4">
						<svg
							xmlns="http://www.w3.org/2000/svg"
							class="stroke-current shrink-0 h-6 w-6"
							fill="none"
							viewBox="0 0 24 24"
						>
							<path
								stroke-linecap="round"
								stroke-linejoin="round"
								stroke-width="2"
								d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"
							/>
						</svg>
						<span>{authActions.getErrorMessage($authState.error)}</span>
					</div>
				{/if}
			</form>
		</div>

		<div class="w-full p-6 text-center">
			Ver 0.9.1460 (2025-09-21)
			{#if isDevelopment}
				<span class="badge badge-warning badge-sm ml-2">개발 버전</span>
			{/if}
		</div>
	</div>
</div>
