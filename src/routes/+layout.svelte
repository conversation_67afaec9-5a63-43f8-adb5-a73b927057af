<script lang="ts">
    import '../app.pcss';
    import UpdateModal from '$lib/components/UpdateModal.svelte';
    import PageRestorer from '$lib/components/PageRestorer.svelte';
    import { onMount } from 'svelte';
    import { initializePushNotificationsByEnvironment } from '$lib/utils/tauri-push-config';

    interface Props {
        children?: import('svelte').Snippet;
    }

    let { children }: Props = $props();

    // 앱 시작 시 푸시 알림 초기화
    onMount(async () => {
        try {
            await initializePushNotificationsByEnvironment();
            console.log('[App] 푸시 알림 초기화 완료');
        } catch (error) {
            console.error('[App] 푸시 알림 초기화 실패:', error);
        }
    });
</script>

{@render children?.()}

<!-- 통합 업데이트 관리 컴포넌트 (자동 + 수동 모달) -->
<UpdateModal />

<!-- 페이지 복원 컴포넌트 -->
<PageRestorer />