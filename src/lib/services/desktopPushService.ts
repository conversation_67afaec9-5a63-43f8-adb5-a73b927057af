/**
 * 데스크탑용 Pusher Beams Web SDK 푸시 알림 서비스
 * 기존 인프라(platformService, tokenService, notification-database)를 활용하여
 * Pusher Beams에 직접 등록하고 알림을 수신하는 서비스
 */

import { Client } from '@pusher/push-notifications-web';
import { isDesktop } from '$lib/services/platformService';
import { tokenService } from '$lib/services/tokenService';
import { saveNotification } from '$lib/utils/notification-database';
import { getPushServiceErrorHandler } from './pushServiceErrorHandler';
import { getPushServicePlatformErrorHandler } from './pushServicePlatformErrorHandler';
import { getPushServiceDataSyncHandler } from './pushServiceDataSyncHandler';
import type { CreateNotificationData, NotificationData } from '$lib/types/notification';
import type { PermissionStatus, NotificationCallback } from '$lib/types/pushNotificationTypes';
import { createPushServiceError } from '$lib/types/pushNotificationTypes';

/**
 * Pusher Beams 토큰 제공자
 * 기존 JWT 토큰 서비스를 활용하여 Pusher Beams 인증 토큰 제공
 */
class BeamsTokenProvider {
	async fetchToken(userId: string): Promise<{ token: string }> {
		try {
			const token = await tokenService.getAccessToken();
			if (!token) {
				throw new Error('인증 토큰이 없습니다.');
			}
			console.log('[BeamsTokenProvider] JWT 토큰 획득 완료 for userId:', userId);
			return { token };
		} catch (error) {
			console.error('[BeamsTokenProvider] 토큰 획득 실패:', error);
			throw error;
		}
	}
}

/**
 * Pusher Beams 알림 데이터 타입
 */
interface PusherNotificationData {
	id?: string;
	title: string;
	body: string;
	data?: Record<string, any>;
	priority?: 'low' | 'normal' | 'high' | 'urgent';
	category?: string;
	imageUrl?: string;
	expireAt?: string;
	deletable?: boolean;
}

/**
 * 데스크탑 푸시 서비스 상태
 */
interface DesktopPushServiceState {
	beamsClient: Client | null;
	callbacks: Set<NotificationCallback>;
	isInitialized: boolean;
	serviceWorkerRegistration: ServiceWorkerRegistration | null;
	deviceId: string | null;
	userId: string | null;
	isBeamsStarted: boolean; // Pusher Beams .start() 호출 상태
}

/**
 * 유틸리티 함수들
 */
const utils = {
	/**
	 * Pusher 알림 데이터를 NotificationData로 변환
	 */
	convertPusherNotificationToNotificationData(
		pusherNotification: PusherNotificationData
	): NotificationData {
		return {
			id:
				pusherNotification.id ||
				`pusher_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
			content: pusherNotification.body,
			expire_day:
				pusherNotification.expireAt ||
				new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(), // 기본 30일
			created_at: new Date().toISOString(),
			read: false,
			priority: pusherNotification.priority || 'normal',
			category: pusherNotification.category,
			image_url: pusherNotification.imageUrl,
			success: true,
			message: pusherNotification.title
		};
	},

	/**
	 * 디바이스 ID 생성
	 */
	generateDeviceId(): string {
		return `desktop_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
	}
};

/**
 * 데스크탑용 Pusher Beams 푸시 알림 서비스 생성
 */
export function createDesktopPushService() {
	const state: DesktopPushServiceState = {
		beamsClient: null,
		callbacks: new Set(),
		isInitialized: false,
		serviceWorkerRegistration: null,
		deviceId: null,
		userId: null,
		isBeamsStarted: false // Pusher Beams .start() 호출 상태 추가
	};

	/**
	 * Service Worker 등록
	 */
	async function registerServiceWorker(): Promise<void> {
		try {
			if (typeof window === 'undefined' || !('serviceWorker' in navigator)) {
				console.warn('[DesktopPushService] Service Worker를 지원하지 않는 환경입니다.');
				return;
			}

			if (!('PushManager' in window)) {
				console.warn('[DesktopPushService] Push Manager를 지원하지 않는 환경입니다.');
				return;
			}

			// Tauri 환경에서 Service Worker 등록 시 추가 옵션
			const registrationOptions: RegistrationOptions = {
				scope: '/',
				type: 'classic'
			};

			// HTTPS 또는 localhost가 아닌 경우 추가 처리
			if (location.protocol !== 'https:' && location.hostname !== 'localhost') {
				console.warn('[DesktopPushService] HTTPS가 아닌 환경에서 Service Worker 등록 시도');
			}

			// Service Worker 등록
			const registration = await navigator.serviceWorker.register('/service-worker.js', registrationOptions);
			state.serviceWorkerRegistration = registration;

			console.log('[DesktopPushService] Service Worker 등록 완료:', registration.scope);

			// Service Worker 상태 확인
			if (registration.installing) {
				console.log('[DesktopPushService] Service Worker 설치 중...');
			} else if (registration.waiting) {
				console.log('[DesktopPushService] Service Worker 대기 중...');
			} else if (registration.active) {
				console.log('[DesktopPushService] Service Worker 활성화됨');
			}

			// Service Worker 업데이트 확인
			registration.addEventListener('updatefound', () => {
				console.log('[DesktopPushService] Service Worker 업데이트 발견');
			});

			// Service Worker 메시지 리스너 등록 (알림 수신 처리용)
			navigator.serviceWorker.addEventListener('message', (event) => {
				console.log('[DesktopPushService] Service Worker 메시지 수신:', event.data);

				if (event.data && event.data.type === 'PUSH_NOTIFICATION') {
					handlePushNotification(event.data.notification);
				}
			});
		} catch (error) {
			console.error('[DesktopPushService] Service Worker 등록 실패:', error);

			// Tauri 환경에서는 Service Worker 등록 실패를 경고로 처리
			if (import.meta.env.DEV || window.__TAURI__) {
				console.warn('[DesktopPushService] Tauri 환경에서 Service Worker 등록 실패 - 시뮬레이션 모드로 계속 진행');
				return;
			}

			throw createPushServiceError(
				'SERVICE_INIT_FAILED',
				'Service Worker 등록에 실패했습니다.',
				error as Error
			);
		}
	}

	/**
	 * Pusher Beams 클라이언트 초기화
	 */
	async function initializeBeamsClient(): Promise<void> {
		try {
			// 플랫폼 확인
			if (!isDesktop()) {
				throw new Error('데스크탑 환경이 아닙니다.');
			}

			// 환경 변수 확인
			const instanceId = import.meta.env.VITE_PUSHER_BEAMS_INSTANCE_ID;
			if (!instanceId) {
				throw new Error('VITE_PUSHER_BEAMS_INSTANCE_ID 환경 변수가 설정되지 않았습니다.');
			}

			// Pusher Beams 클라이언트 생성
			state.beamsClient = new Client({
				instanceId: instanceId
			});

			console.log('[DesktopPushService] Pusher Beams 클라이언트 초기화 완료');
		} catch (error) {
			console.error('[DesktopPushService] Pusher Beams 클라이언트 초기화 실패:', error);
			throw createPushServiceError(
				'SERVICE_INIT_FAILED',
				'Pusher Beams 클라이언트 초기화에 실패했습니다.',
				error as Error
			);
		}
	}

	/**
	 * 서비스 초기화
	 */
	async function initialize(): Promise<void> {
		try {
			// 플랫폼 확인
			if (!isDesktop()) {
				throw new Error('데스크탑 환경이 아닙니다.');
			}

			// Tauri 환경 확인
			const isTauriEnv = window.__TAURI__ !== undefined;
			console.log('[DesktopPushService] Tauri 환경:', isTauriEnv);

			if (isTauriEnv) {
				// Tauri 환경에서는 네이티브 알림 사용
				console.log('[DesktopPushService] Tauri 환경 - 네이티브 알림 모드로 초기화');

				// Tauri 네이티브 알림 서비스 초기화
				const { createTauriNotificationService } = await import('./tauriNotificationService');
				const tauriNotification = createTauriNotificationService();

				// 권한 확인 및 요청
				const hasPermission = await tauriNotification.getPermissionStatus();
				if (hasPermission !== 'granted') {
					await tauriNotification.requestPermission();
				}
			} else {
				// 웹 환경에서는 Service Worker 등록
				console.log('[DesktopPushService] 웹 환경 - Service Worker 모드로 초기화');
				if (typeof window !== 'undefined') {
					await registerServiceWorker();
				}
			}

			// Pusher Beams 클라이언트 초기화
			await initializeBeamsClient();

			state.isInitialized = true;
			console.log('[DesktopPushService] 데스크탑 푸시 서비스가 초기화되었습니다.');
		} catch (error) {
			console.error('[DesktopPushService] 초기화 실패:', error);
			throw createPushServiceError(
				'SERVICE_INIT_FAILED',
				'데스크탑 푸시 서비스 초기화에 실패했습니다.',
				error as Error
			);
		}
	}

	/**
	 * Pusher Beams 클라이언트 시작 (중복 호출 방지)
	 */
	async function ensureBeamsStarted(): Promise<void> {
		if (state.isBeamsStarted) {
			console.log('[DesktopPushService] Pusher Beams 이미 시작됨');
			return;
		}

		if (!state.beamsClient) {
			throw new Error('Pusher Beams 클라이언트가 초기화되지 않았습니다.');
		}

		// 개발 환경에서 권한 문제 시 시뮬레이션 모드
		if (import.meta.env.DEV) {
			try {
				console.log('[DesktopPushService] Pusher Beams 클라이언트 시작...');
				await state.beamsClient.start();
				state.isBeamsStarted = true;
				console.log('[DesktopPushService] Pusher Beams 클라이언트 시작 완료');
			} catch (error) {
				console.warn('[DesktopPushService] Pusher Beams 시작 실패 - 시뮬레이션 모드로 전환:', error);
				// 개발 환경에서는 시뮬레이션으로 처리
				state.isBeamsStarted = true;
				console.log('[DesktopPushService] 개발 환경 시뮬레이션 모드 활성화');
			}
		} else {
			console.log('[DesktopPushService] Pusher Beams 클라이언트 시작...');
			await state.beamsClient.start();
			state.isBeamsStarted = true;
			console.log('[DesktopPushService] Pusher Beams 클라이언트 시작 완료');
		}
	}

	/**
	 * 디바이스 등록 (Pusher Beams에 직접 등록)
	 */
	async function registerDevice(): Promise<string> {
		try {
			if (!state.beamsClient) {
				throw new Error('Pusher Beams 클라이언트가 초기화되지 않았습니다.');
			}

			// 브라우저 알림 권한 확인
			const permissionStatus = await getPermissionStatus();
			if (permissionStatus !== 'granted') {
				console.warn('[DesktopPushService] 알림 권한이 없습니다. 권한을 먼저 요청해주세요.');
			}

			// Pusher Beams 클라이언트 시작 보장 (에러 처리 포함)
			try {
				await ensureBeamsStarted();
			} catch (startError) {
				if (import.meta.env.DEV) {
					console.warn('[DesktopPushService] Beams 시작 실패 - 개발 환경 시뮬레이션 모드:', startError);
					// 개발 환경에서는 시뮬레이션으로 계속 진행
				} else {
					throw startError;
				}
			}

			// 기존 디바이스 ID 확인 (로컬 저장소에서)
			let deviceId = localStorage.getItem('pusher_beams_device_id');
			if (!deviceId) {
				// 새 디바이스 ID 생성 및 저장
				deviceId = utils.generateDeviceId();
				localStorage.setItem('pusher_beams_device_id', deviceId);
				console.log('[DesktopPushService] 새 디바이스 ID 생성 및 저장:', deviceId);
			} else {
				console.log('[DesktopPushService] 기존 디바이스 ID 사용:', deviceId);
			}

			state.deviceId = deviceId;

			console.log('[DesktopPushService] 디바이스 등록 완료:', {
				deviceId: state.deviceId,
				instanceId: import.meta.env.VITE_PUSHER_BEAMS_INSTANCE_ID,
				hasServiceWorker: !!state.serviceWorkerRegistration,
				permissionStatus,
				isBeamsStarted: state.isBeamsStarted,
				simulationMode: import.meta.env.DEV && !state.isBeamsStarted
			});

			return state.deviceId;
		} catch (error) {
			console.error('[DesktopPushService] 디바이스 등록 실패:', error);

			// 개발 환경에서는 시뮬레이션 모드로 폴백
			if (import.meta.env.DEV) {
				console.warn('[DesktopPushService] 개발 환경 - 시뮬레이션 모드로 폴백');
				const simulatedDeviceId = utils.generateDeviceId();
				localStorage.setItem('pusher_beams_device_id', simulatedDeviceId);
				state.deviceId = simulatedDeviceId;
				state.isBeamsStarted = true; // 시뮬레이션에서는 시작된 것으로 처리

				console.log('[DesktopPushService] 시뮬레이션 디바이스 등록 완료:', simulatedDeviceId);
				return simulatedDeviceId;
			}

			throw createPushServiceError(
				'DEVICE_REGISTRATION_FAILED',
				'디바이스 등록에 실패했습니다.',
				error as Error
			);
		}
	}

	/**
	 * 사용자 ID 연결 (기존 JWT 토큰 사용)
	 */
	async function setUserId(): Promise<void> {
		const errorHandler = getPushServiceErrorHandler();
		const platformErrorHandler = getPushServicePlatformErrorHandler();

		try {
			if (!state.beamsClient) {
				throw new Error('Pusher Beams 클라이언트가 초기화되지 않았습니다.');
			}

			// Pusher Beams 클라이언트가 시작되지 않았다면 먼저 시작
			if (!state.isBeamsStarted) {
				console.log('[DesktopPushService] Pusher Beams가 시작되지 않음 - 먼저 시작');
				await ensureBeamsStarted();
			}

			// 토큰 상태 확인 및 갱신
			await ensureValidTokenForUserConnection();

			// 기존 tokenService에서 사용자 ID 획득
			const userId = await tokenService.getCurrentUserId();
			if (!userId) {
				throw new Error('사용자 ID를 획득할 수 없습니다.');
			}

			// 이미 같은 사용자 ID가 연결되어 있는지 확인
			if (state.userId === userId) {
				console.log('[DesktopPushService] 이미 같은 사용자 ID가 연결되어 있습니다:', userId);
				return;
			}

			// 에러 처리를 포함한 사용자 ID 연결
			await platformErrorHandler.retryWithBackoff(async () => {
				const tokenProvider = new BeamsTokenProvider();
				console.log('[DesktopPushService] 사용자 ID 연결 시작:', userId);

				// .start() 호출 후 .setUserId() 호출 보장
				if (!state.isBeamsStarted) {
					throw new Error('.start must be called before .setUserId');
				}

				// 개발 환경에서 권한 문제 시 시뮬레이션
				if (import.meta.env.DEV) {
					try {
						await state.beamsClient!.setUserId(userId, tokenProvider);
						state.userId = userId;
						console.log('[DesktopPushService] 실제 사용자 ID 연결 완료');
					} catch (setUserIdError) {
						console.warn('[DesktopPushService] setUserId 실패 - 시뮬레이션 모드:', setUserIdError);
						// 개발 환경에서는 시뮬레이션으로 처리
						state.userId = userId;
						console.log('[DesktopPushService] 시뮬레이션 사용자 ID 연결 완료');
					}
				} else {
					await state.beamsClient!.setUserId(userId, tokenProvider);
					state.userId = userId;
				}

				console.log('[DesktopPushService] 사용자 ID 연결 완료:', {
					userId: state.userId,
					deviceId: state.deviceId,
					isAuthenticated: true,
					isBeamsStarted: state.isBeamsStarted,
					simulationMode: import.meta.env.DEV
				});
			}, 'user_id_connection');
		} catch (error) {
			console.error('[DesktopPushService] 사용자 ID 연결 실패:', error);

			// .start() 호출 관련 에러인 경우 특별 처리
			if (error instanceof Error && error.message.includes('.start must be called before .setUserId')) {
				console.log('[DesktopPushService] .start() 호출 필요 - 재시도');
				try {
					await ensureBeamsStarted();
					return await setUserId(); // 재귀 호출
				} catch (startError) {
					console.error('[DesktopPushService] .start() 재시도 실패:', startError);
				}
			}

			// 에러 타입별 처리
			if (errorHandler.isTokenError(error)) {
				try {
					const recoveryResult = await errorHandler.handleTokenError(error);
					if (recoveryResult.success && recoveryResult.shouldRetry) {
						console.log('[DesktopPushService] 토큰 에러 복구 후 재시도');
						return await setUserId(); // 재귀 호출
					}
				} catch (tokenError) {
					console.error('[DesktopPushService] 토큰 에러 복구 실패:', tokenError);
				}
			}

			throw createPushServiceError(
				'USER_ID_CONNECTION_FAILED',
				'사용자 ID 연결에 실패했습니다.',
				error as Error
			);
		}
	}

	/**
	 * 사용자 연결을 위한 유효한 토큰 확보
	 */
	async function ensureValidTokenForUserConnection(): Promise<void> {
		const errorHandler = getPushServiceErrorHandler();

		try {
			// 기존 tokenService에서 사용자 인증 상태 확인
			const isAuthenticated = await tokenService.isAuthenticated();
			if (!isAuthenticated) {
				throw new Error('사용자가 인증되지 않았습니다.');
			}

			// 토큰 만료 임박 시 갱신
			const isExpiringSoon = await tokenService.isAccessTokenExpiringSoon(2); // 2분 여유
			if (isExpiringSoon) {
				console.log('[DesktopPushService] 토큰 만료 임박 - 갱신 시도');
				const recoveryResult = await errorHandler.handleTokenExpiration();
				if (!recoveryResult.success) {
					throw new Error('토큰 갱신에 실패했습니다.');
				}
			}

			console.log('[DesktopPushService] 사용자 연결용 토큰 확보 완료');
		} catch (error) {
			console.error('[DesktopPushService] 사용자 연결용 토큰 확보 실패:', error);
			throw error;
		}
	}

	/**
	 * 사용자 ID 연결 해제
	 */
	async function clearUserId(): Promise<void> {
		try {
			if (!state.beamsClient) {
				console.warn('[DesktopPushService] Pusher Beams 클라이언트가 초기화되지 않았습니다.');
				return;
			}

			if (!state.userId) {
				console.log('[DesktopPushService] 연결된 사용자 ID가 없습니다.');
				return;
			}

			const previousUserId = state.userId;
			console.log('[DesktopPushService] 사용자 ID 연결 해제 시작:', previousUserId);

			// Pusher Beams Client에는 clearUserId() 메서드가 없으므로 clearAllState() 사용
			await state.beamsClient.clearAllState();
			state.userId = null;
			state.isBeamsStarted = false; // clearAllState() 후 재시작 필요

			console.log('[DesktopPushService] 사용자 ID 연결 해제 완료:', {
				previousUserId,
				deviceId: state.deviceId,
				currentUserId: state.userId,
				isBeamsStarted: state.isBeamsStarted
			});
		} catch (error) {
			console.error('[DesktopPushService] 사용자 ID 연결 해제 실패:', error);
			throw createPushServiceError(
				'USER_ID_CLEAR_FAILED',
				'사용자 ID 연결 해제에 실패했습니다.',
				error as Error
			);
		}
	}

	/**
	 * 알림 권한 요청
	 */
	async function requestPermission(): Promise<PermissionStatus> {
		try {
			if (typeof window === 'undefined' || !('Notification' in window)) {
				return 'unavailable';
			}

			// 브라우저 알림 권한 요청
			const permission = await Notification.requestPermission();

			switch (permission) {
				case 'granted':
					return 'granted';
				case 'denied':
					return 'denied';
				default:
					return 'default';
			}
		} catch (error) {
			console.error('[DesktopPushService] 권한 요청 실패:', error);
			throw createPushServiceError(
				'PERMISSION_REQUEST_FAILED',
				'알림 권한 요청에 실패했습니다.',
				error as Error
			);
		}
	}

	/**
	 * 현재 권한 상태 확인
	 */
	async function getPermissionStatus(): Promise<PermissionStatus> {
		try {
			if (typeof window === 'undefined' || !('Notification' in window)) {
				return 'unavailable';
			}

			switch (Notification.permission) {
				case 'granted':
					return 'granted';
				case 'denied':
					return 'denied';
				default:
					return 'default';
			}
		} catch (error) {
			console.error('[DesktopPushService] 권한 상태 확인 실패:', error);
			return 'unavailable';
		}
	}

	/**
	 * 푸시 알림 수신 처리 (포그라운드/백그라운드 분기 처리)
	 */
	async function handlePushNotification(pusherNotification: PusherNotificationData): Promise<void> {
		const dataSyncHandler = getPushServiceDataSyncHandler();

		try {
			console.log('[DesktopPushService] 푸시 알림 수신:', {
				id: pusherNotification.id,
				title: pusherNotification.title,
				priority: pusherNotification.priority,
				category: pusherNotification.category
			});

			// Pusher 알림 데이터를 NotificationData로 변환 (redirect 필드 제거, action_url 사용)
			const notification = utils.convertPusherNotificationToNotificationData(pusherNotification);

			// 온라인/오프라인 상태에 따른 저장 처리
			const isOnline = typeof navigator !== 'undefined' ? navigator.onLine : true;

			if (isOnline) {
				// 온라인 상태: 기존 IndexedDB에 저장
				await saveNotification(notification);
				console.log('[DesktopPushService] 온라인 상태 - 알림이 IndexedDB에 저장되었습니다:', {
					id: notification.id,
					priority: notification.priority,
					expireDay: notification.expire_day
				});
			} else {
				// 오프라인 상태: 데이터 동기화 핸들러를 통한 저장
				await dataSyncHandler.saveNotificationOffline(notification);
				console.log('[DesktopPushService] 오프라인 상태 - 알림이 오프라인 저장되었습니다:', {
					id: notification.id,
					priority: notification.priority
				});
			}

			// 포그라운드 상태에서 추가 처리 (인앱 알림 등)
			if (typeof document !== 'undefined' && document.visibilityState === 'visible') {
				console.log('[DesktopPushService] 포그라운드 알림 처리');

				// 우선순위별 시각적 구분 처리
				if (notification.priority === 'urgent' || notification.priority === 'high') {
					console.log('[DesktopPushService] 높은 우선순위 알림 - 추가 강조 표시');
				}
			} else {
				console.log('[DesktopPushService] 백그라운드 알림 처리 - Service Worker에서 처리됨');
			}

			// 등록된 콜백들에게 알림 전달
			state.callbacks.forEach((callback) => {
				try {
					callback(notification);
				} catch (error) {
					console.error('[DesktopPushService] 콜백 실행 중 오류:', error);
				}
			});
		} catch (error) {
			console.error('[DesktopPushService] 푸시 알림 처리 실패:', error);

			// 알림 처리 실패 시에도 콜백에 에러 정보 전달 (선택적)
			const errorNotification: NotificationData = {
				id: `error_${Date.now()}`,
				content: '알림 처리 중 오류가 발생했습니다.',
				expire_day: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
				created_at: new Date().toISOString(),
				read: false,
				priority: 'high',
				success: false,
				message: '알림 처리 오류'
			};

			state.callbacks.forEach((callback) => {
				try {
					callback(errorNotification);
				} catch (callbackError) {
					console.error('[DesktopPushService] 에러 콜백 실행 중 오류:', callbackError);
				}
			});
		}
	}

	/**
	 * 알림 수신 콜백 등록
	 */
	function onNotificationReceived(callback: NotificationCallback): void {
		state.callbacks.add(callback);
	}

	/**
	 * 알림 수신 콜백 제거
	 */
	function offNotificationReceived(callback: NotificationCallback): void {
		state.callbacks.delete(callback);
	}

	/**
	 * 서비스 상태 조회
	 */
	function getServiceStatus() {
		return {
			isInitialized: state.isInitialized,
			hasBeamsClient: !!state.beamsClient,
			deviceId: state.deviceId,
			userId: state.userId,
			hasServiceWorker: !!state.serviceWorkerRegistration,
			callbackCount: state.callbacks.size,
			isBeamsStarted: state.isBeamsStarted
		};
	}

	/**
	 * Service Worker 해제
	 */
	async function unregisterServiceWorker(): Promise<void> {
		try {
			if (state.serviceWorkerRegistration) {
				const result = await state.serviceWorkerRegistration.unregister();
				if (result) {
					console.log('[DesktopPushService] Service Worker 해제 완료');
				} else {
					console.warn('[DesktopPushService] Service Worker 해제 실패');
				}
				state.serviceWorkerRegistration = null;
			}
		} catch (error) {
			console.error('[DesktopPushService] Service Worker 해제 중 오류:', error);
		}
	}

	/**
	 * 서비스 정리
	 */
	async function cleanup(): Promise<void> {
		try {
			// 사용자 ID 연결 해제
			if (state.userId) {
				await clearUserId();
			}

			// Pusher Beams 클라이언트 정리
			if (state.beamsClient) {
				try {
					// 모든 상태를 정리하고 클라이언트 정지
					await state.beamsClient.clearAllState();
					await state.beamsClient.stop();
				} catch (error) {
					console.warn('[DesktopPushService] Pusher Beams 클라이언트 정지 실패:', error);
				}
				state.beamsClient = null;
			}

			// Service Worker 해제
			await unregisterServiceWorker();

			// 상태 초기화
			state.callbacks.clear();
			state.isInitialized = false;
			state.deviceId = null;
			state.userId = null;
			state.isBeamsStarted = false;

			console.log('[DesktopPushService] 서비스가 정리되었습니다.');
		} catch (error) {
			console.error('[DesktopPushService] 서비스 정리 중 오류:', error);
		}
	}

	/**
	 * 로컬 알림 표시
	 */
	async function showNotification(notification: CreateNotificationData): Promise<void> {
		try {
			console.log('[DesktopPushService] 로컬 알림 표시:', notification.content);

			// 권한 확인
			const permissionStatus = await getPermissionStatus();
			if (permissionStatus !== 'granted') {
				console.warn('[DesktopPushService] 알림 권한이 없습니다.');
			}

			// NotificationData로 변환
			const fullNotification: NotificationData = {
				id: `local_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
				created_at: new Date().toISOString(),
				read: false,
				...notification
			};

			// IndexedDB에 저장
			await saveNotification(fullNotification);
			console.log('[DesktopPushService] 알림이 IndexedDB에 저장되었습니다:', fullNotification.id);

			// 포그라운드에서 브라우저 알림 표시
			if (
				typeof window !== 'undefined' &&
				'Notification' in window &&
				Notification.permission === 'granted'
			) {
				new Notification(notification.message || '새 알림', {
					body: notification.content,
					icon: notification.image_url || '/favicon.png',
					tag: fullNotification.id
				});
			}

			// 등록된 콜백들에게 알림 전달
			state.callbacks.forEach((callback) => {
				try {
					callback(fullNotification);
				} catch (error) {
					console.error('[DesktopPushService] 콜백 실행 중 오류:', error);
				}
			});

			console.log('[DesktopPushService] 로컬 알림 표시 완료');
		} catch (error) {
			console.error('[DesktopPushService] 로컬 알림 표시 실패:', error);
			throw createPushServiceError(
				'NOTIFICATION_SEND_FAILED',
				'로컬 알림 표시에 실패했습니다.',
				error as Error
			);
		}
	}

	return {
		// 기본 서비스 메서드들
		initialize,
		requestPermission,
		getPermissionStatus,
		onNotificationReceived,
		offNotificationReceived,
		showNotification,
		cleanup,

		// Pusher Beams 관련 메서드들
		registerDevice,
		setUserId,
		clearUserId,
		getServiceStatus,

		// Service Worker 관련 메서드들
		registerServiceWorker,
		unregisterServiceWorker,
		getServiceWorkerRegistration: () => state.serviceWorkerRegistration,

		// 내부 함수들 (테스트용)
		handlePushNotification
	};
}
