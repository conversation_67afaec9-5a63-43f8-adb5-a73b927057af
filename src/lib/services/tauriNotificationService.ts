/**
 * Tauri 네이티브 알림 서비스
 * Tauri의 notification 플러그인을 사용한 네이티브 알림 처리
 */

import { isPermissionGranted, requestPermission, sendNotification } from '@tauri-apps/plugin-notification';
import type { PermissionStatus } from '$lib/types/pushNotificationTypes';
import type { NotificationData } from '$lib/types/notification';
import { data } from 'autoprefixer';

/**
 * Tauri 네이티브 알림 서비스 생성
 */
export function createTauriNotificationService() {
	/**
	 * 알림 권한 상태 확인 (개선된 버전)
	 */
	async function getPermissionStatus(): Promise<PermissionStatus> {
		try {
			const api = await loadTauriNotificationAPI();
			const granted = await api.isPermissionGranted();
			console.log('[TauriNotification] 권한 상태:', granted);
			return granted ? 'granted' : 'denied';
		} catch (error) {
			console.error('[TauriNotification] 권한 상태 확인 실패:', error);
			return 'denied';
		}
	}

	/**
	 * 알림 권한 요청 (개선된 버전)
	 */
	async function requestPermissionStatus(): Promise<PermissionStatus> {
		try {
			console.log('[TauriNotification] 알림 권한 요청 중...');
			const api = await loadTauriNotificationAPI();
			const permission = await api.requestPermission();
			console.log('[TauriNotification] 권한 요청 결과:', permission);
			return permission === 'granted' ? 'granted' : 'denied';
		} catch (error) {
			console.error('[TauriNotification] 권한 요청 실패:', error);

			// IPC 통신 실패 시 브라우저 권한으로 폴백
			if ('Notification' in window) {
				console.log('[TauriNotification] 브라우저 알림 권한으로 폴백');
				try {
					const browserPermission = await Notification.requestPermission();
					return browserPermission === 'granted' ? 'granted' : 'denied';
				} catch (browserError) {
					console.error('[TauriNotification] 브라우저 권한 요청도 실패:', browserError);
				}
			}

			return 'denied';
		}
	}

	/**
	 * 네이티브 알림 표시 (개선된 버전)
	 */
	async function showNotification(notification: NotificationData): Promise<void> {
		try {
			console.log('[TauriNotification] 네이티브 알림 표시 시도:', notification.title);

			const api = await loadTauriNotificationAPI();

			// 권한 확인
			const hasPermission = await api.isPermissionGranted();
			if (!hasPermission) {
				console.warn('[TauriNotification] 알림 권한이 없습니다.');

				// 권한 요청 시도
				const permission = await api.requestPermission();
				if (permission !== 'granted') {
					throw new Error('알림 권한이 거부되었습니다.');
				}
			}

			// Tauri 알림 표시
			await api.sendNotification({
				title: notification.title,
				body: notification.body || notification.content || '',
				icon: notification.icon || '/favicon.png'
			});

			console.log('[TauriNotification] 네이티브 알림 표시 완료:', notification.title);
		} catch (error) {
			console.error('[TauriNotification] 알림 표시 실패:', error);

			// 폴백: 브라우저 알림 시도
			if ('Notification' in window && Notification.permission === 'granted') {
				console.log('[TauriNotification] 브라우저 알림으로 폴백');
				new Notification(notification.title, {
					body: notification.body || notification.content || '',
					icon: notification.icon || '/favicon.png'
				});
			} else {
				throw error;
			}
		}
	}

	/**
	 * 권한 상태와 함께 알림 표시
	 */
	async function showNotificationWithPermissionCheck(notification: NotificationData): Promise<boolean> {
		try {
			// 권한 확인
			let hasPermission = await isPermissionGranted();
			
			// 권한이 없으면 요청
			if (!hasPermission) {
				const permission = await requestPermission();
				hasPermission = permission === 'granted';
			}

			if (!hasPermission) {
				console.warn('[TauriNotification] 알림 권한이 거부되었습니다.');
				return false;
			}

			// 알림 표시
			await showNotification(notification);
			return true;
		} catch (error) {
			console.error('[TauriNotification] 권한 확인 및 알림 표시 실패:', error);
			return false;
		}
	}

	/**
	 * 서비스 사용 가능 여부 확인 (개선된 버전)
	 */
	function isAvailable(): boolean {
		// 다중 조건으로 Tauri 환경 확인
		if (typeof window === 'undefined') {
			return false;
		}

		// 1. __TAURI__ 객체 확인
		if (window.__TAURI__ !== undefined) {
			return true;
		}

		// 2. Tauri 관련 프로토콜 확인
		if (window.location.protocol === 'tauri:' || window.location.hostname === 'ipc.localhost') {
			return true;
		}

		// 3. User Agent 확인
		if (navigator.userAgent.includes('Tauri')) {
			return true;
		}

		return false;
	}

	/**
	 * Tauri API 동적 로드 및 오류 처리
	 */
	async function loadTauriNotificationAPI() {
		try {
			const api = await import('@tauri-apps/plugin-notification');
			console.log('[TauriNotification] Tauri 알림 API 로드 성공');
			return api;
		} catch (error) {
			console.error('[TauriNotification] Tauri 알림 API 로드 실패:', error);
			throw new Error('Tauri 알림 API를 로드할 수 없습니다.');
		}
	}

	return {
		getPermissionStatus,
		requestPermission: requestPermissionStatus,
		showNotification,
		showNotificationWithPermissionCheck,
		isAvailable
	};
}

/**
 * Tauri 환경에서 웹 푸시 대신 네이티브 알림 사용
 */
export function createTauriPushNotificationFallback() {
	const tauriNotification = createTauriNotificationService();

	/**
	 * 푸시 알림을 네이티브 알림으로 변환하여 표시
	 */
	async function handlePushNotificationAsFallback(data: any): Promise<void> {
		try {
			// 푸시 데이터를 네이티브 알림 형식으로 변환
			const notification: NotificationData = {
				content: '',
				id: data.id || Date.now().toString(),
				title: data.title || data.notification?.title || '새 알림',
				body: data.body || data.notification?.body || data.notification?.content || data.message,
				icon: data.icon || data.notification?.icon || '/favicon.png',
				created_at: new Date().toISOString(),
				expire_day: '',
				read: false,
				category: data.category || 'general',
				priority: data.priority || 'normal',
				deletable: data.deletable !== false
			};

			// 네이티브 알림으로 표시
			const success = await tauriNotification.showNotificationWithPermissionCheck(notification);
			
			if (success) {
				console.log('[TauriPushFallback] 푸시 알림을 네이티브 알림으로 표시 완료');
			} else {
				console.warn('[TauriPushFallback] 네이티브 알림 표시 실패');
			}
		} catch (error) {
			console.error('[TauriPushFallback] 푸시 알림 폴백 처리 실패:', error);
		}
	}

	/**
	 * Pusher Beams 웹 푸시 대신 시뮬레이션
	 */
	async function simulatePusherBeamsWithNativeNotification(): Promise<void> {
		console.log('[TauriPushFallback] Pusher Beams 웹 푸시를 네이티브 알림으로 시뮬레이션');
		
		// 테스트 알림 표시
		const testNotification: NotificationData = {
			content: '', expire_day: '',
			id: 'tauri-test-' + Date.now(),
			title: 'CNSPROWMS',
			body: 'Tauri 네이티브 알림이 정상적으로 작동합니다.',
			icon: '/favicon.png',
			created_at: new Date().toISOString(),
			read: false,
			category: 'system',
			priority: 'normal',
			deletable: true
		};

		await tauriNotification.showNotificationWithPermissionCheck(testNotification);
	}

	return {
		handlePushNotificationAsFallback,
		simulatePusherBeamsWithNativeNotification,
		isAvailable: tauriNotification.isAvailable
	};
}
