/**
 * Tauri 네이티브 알림 서비스
 * Tauri의 notification 플러그인을 사용한 네이티브 알림 처리
 */

import { isPermissionGranted, requestPermission, sendNotification } from '@tauri-apps/plugin-notification';
import type { PermissionStatus } from '$lib/types/pushNotificationTypes';
import type { NotificationData } from '$lib/types/notification';
import { data } from 'autoprefixer';

/**
 * Tauri 네이티브 알림 서비스 생성
 */
export function createTauriNotificationService() {
	/**
	 * 알림 권한 상태 확인
	 */
	async function getPermissionStatus(): Promise<PermissionStatus> {
		try {
			const granted = await isPermissionGranted();
			return granted ? 'granted' : 'denied';
		} catch (error) {
			console.error('[TauriNotification] 권한 상태 확인 실패:', error);
			return 'denied';
		}
	}

	/**
	 * 알림 권한 요청
	 */
	async function requestPermissionStatus(): Promise<PermissionStatus> {
		try {
			const permission = await requestPermission();
			return permission === 'granted' ? 'granted' : 'denied';
		} catch (error) {
			console.error('[TauriNotification] 권한 요청 실패:', error);
			return 'denied';
		}
	}

	/**
	 * 네이티브 알림 표시
	 */
	async function showNotification(notification: NotificationData): Promise<void> {
		try {
			// 권한 확인
			const hasPermission = await isPermissionGranted();
			if (!hasPermission) {
				console.warn('[TauriNotification] 알림 권한이 없습니다.');
				return;
			}

			// Tauri 알림 표시
			await sendNotification({
				title: notification.title,
				body: notification.body || notification.content,
				icon: notification.icon || '/favicon.png'
			});

			console.log('[TauriNotification] 네이티브 알림 표시 완료:', notification.title);
		} catch (error) {
			console.error('[TauriNotification] 알림 표시 실패:', error);
			throw error;
		}
	}

	/**
	 * 권한 상태와 함께 알림 표시
	 */
	async function showNotificationWithPermissionCheck(notification: NotificationData): Promise<boolean> {
		try {
			// 권한 확인
			let hasPermission = await isPermissionGranted();
			
			// 권한이 없으면 요청
			if (!hasPermission) {
				const permission = await requestPermission();
				hasPermission = permission === 'granted';
			}

			if (!hasPermission) {
				console.warn('[TauriNotification] 알림 권한이 거부되었습니다.');
				return false;
			}

			// 알림 표시
			await showNotification(notification);
			return true;
		} catch (error) {
			console.error('[TauriNotification] 권한 확인 및 알림 표시 실패:', error);
			return false;
		}
	}

	/**
	 * 서비스 사용 가능 여부 확인
	 */
	function isAvailable(): boolean {
		return window.__TAURI__ !== undefined;
	}

	return {
		getPermissionStatus,
		requestPermission: requestPermissionStatus,
		showNotification,
		showNotificationWithPermissionCheck,
		isAvailable
	};
}

/**
 * Tauri 환경에서 웹 푸시 대신 네이티브 알림 사용
 */
export function createTauriPushNotificationFallback() {
	const tauriNotification = createTauriNotificationService();

	/**
	 * 푸시 알림을 네이티브 알림으로 변환하여 표시
	 */
	async function handlePushNotificationAsFallback(data: any): Promise<void> {
		try {
			// 푸시 데이터를 네이티브 알림 형식으로 변환
			const notification: NotificationData = {
				content: '',
				id: data.id || Date.now().toString(),
				title: data.title || data.notification?.title || '새 알림',
				body: data.body || data.notification?.body || data.notification?.content || data.message,
				icon: data.icon || data.notification?.icon || '/favicon.png',
				created_at: new Date().toISOString(),
				expire_day: '',
				read: false,
				category: data.category || 'general',
				priority: data.priority || 'normal',
				deletable: data.deletable !== false
			};

			// 네이티브 알림으로 표시
			const success = await tauriNotification.showNotificationWithPermissionCheck(notification);
			
			if (success) {
				console.log('[TauriPushFallback] 푸시 알림을 네이티브 알림으로 표시 완료');
			} else {
				console.warn('[TauriPushFallback] 네이티브 알림 표시 실패');
			}
		} catch (error) {
			console.error('[TauriPushFallback] 푸시 알림 폴백 처리 실패:', error);
		}
	}

	/**
	 * Pusher Beams 웹 푸시 대신 시뮬레이션
	 */
	async function simulatePusherBeamsWithNativeNotification(): Promise<void> {
		console.log('[TauriPushFallback] Pusher Beams 웹 푸시를 네이티브 알림으로 시뮬레이션');
		
		// 테스트 알림 표시
		const testNotification: NotificationData = {
			content: '', expire_day: '',
			id: 'tauri-test-' + Date.now(),
			title: 'CNSPROWMS',
			body: 'Tauri 네이티브 알림이 정상적으로 작동합니다.',
			icon: '/favicon.png',
			created_at: new Date().toISOString(),
			read: false,
			category: 'system',
			priority: 'normal',
			deletable: true
		};

		await tauriNotification.showNotificationWithPermissionCheck(testNotification);
	}

	return {
		handlePushNotificationAsFallback,
		simulatePusherBeamsWithNativeNotification,
		isAvailable: tauriNotification.isAvailable
	};
}
