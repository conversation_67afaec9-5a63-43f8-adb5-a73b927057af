/**
 * Tauri 환경에서의 푸시 알림 설정 관리
 */

export interface TauriPushConfig {
	useNativeNotifications: boolean;
	fallbackToServiceWorker: boolean;
	enablePusherBeams: boolean;
	debugMode: boolean;
}

/**
 * Tauri 환경 감지
 */
export function isTauriEnvironment(): boolean {
	return typeof window !== 'undefined' && window.__TAURI__ !== undefined;
}

/**
 * 개발 환경 감지
 */
export function isDevelopmentEnvironment(): boolean {
	return import.meta.env.DEV || import.meta.env.VITE_NODE_ENV === 'development';
}

/**
 * Tauri 푸시 알림 설정 가져오기
 */
export function getTauriPushConfig(): TauriPushConfig {
	const isTauri = isTauriEnvironment();
	const isDev = isDevelopmentEnvironment();

	return {
		// Tauri 환경에서는 네이티브 알림 우선 사용
		useNativeNotifications: isTauri,
		
		// Tauri 환경에서 네이티브 알림 실패 시 Service Worker로 폴백
		fallbackToServiceWorker: isTauri && isDev,
		
		// 개발 환경에서는 Pusher Beams 시뮬레이션 모드
		enablePusherBeams: !isTauri || isDev,
		
		// 개발 환경에서 디버그 모드 활성화
		debugMode: isDev
	};
}

/**
 * 푸시 알림 전략 결정
 */
export function getPushNotificationStrategy(): 'native' | 'service-worker' | 'simulation' {
	const config = getTauriPushConfig();
	const isTauri = isTauriEnvironment();
	const isDev = isDevelopmentEnvironment();

	if (isTauri && config.useNativeNotifications) {
		return 'native';
	}

	if (!isTauri && 'serviceWorker' in navigator) {
		return 'service-worker';
	}

	// 폴백 또는 개발 환경
	return 'simulation';
}

/**
 * 환경별 로깅
 */
export function logEnvironmentInfo(): void {
	const config = getTauriPushConfig();
	const strategy = getPushNotificationStrategy();
	
	console.group('[TauriPushConfig] 환경 정보');
	console.log('Tauri 환경:', isTauriEnvironment());
	console.log('개발 환경:', isDevelopmentEnvironment());
	console.log('푸시 전략:', strategy);
	console.log('설정:', config);
	console.groupEnd();
}

/**
 * 권한 요청 전략
 */
export async function requestNotificationPermissionByStrategy(): Promise<'granted' | 'denied' | 'default'> {
	const strategy = getPushNotificationStrategy();
	
	try {
		switch (strategy) {
			case 'native': {
				// Tauri 네이티브 알림 권한 요청
				const { isPermissionGranted, requestPermission } = await import('@tauri-apps/plugin-notification');
				
				const hasPermission = await isPermissionGranted();
				if (hasPermission) {
					return 'granted';
				}
				
				const permission = await requestPermission();
				return permission === 'granted' ? 'granted' : 'denied';
			}
			
			case 'service-worker': {
				// 웹 푸시 알림 권한 요청
				if ('Notification' in window) {
					const permission = await Notification.requestPermission();
					return permission;
				}
				return 'denied';
			}
			
			case 'simulation': {
				// 시뮬레이션 모드에서는 항상 허용
				console.log('[TauriPushConfig] 시뮬레이션 모드 - 권한 허용');
				return 'granted';
			}
			
			default:
				return 'denied';
		}
	} catch (error) {
		console.error('[TauriPushConfig] 권한 요청 실패:', error);
		return 'denied';
	}
}

/**
 * 알림 표시 전략
 */
export async function showNotificationByStrategy(
	title: string, 
	options: NotificationOptions & { body?: string }
): Promise<boolean> {
	const strategy = getPushNotificationStrategy();
	
	try {
		switch (strategy) {
			case 'native': {
				// Tauri 네이티브 알림
				const { sendNotification } = await import('@tauri-apps/plugin-notification');
				
				await sendNotification({
					title,
					body: options.body || '',
					icon: options.icon || '/favicon.png'
				});
				
				console.log('[TauriPushConfig] 네이티브 알림 표시 완료');
				return true;
			}
			
			case 'service-worker': {
				// 웹 알림
				if ('Notification' in window && Notification.permission === 'granted') {
					new Notification(title, options);
					console.log('[TauriPushConfig] 웹 알림 표시 완료');
					return true;
				}
				return false;
			}
			
			case 'simulation': {
				// 시뮬레이션 모드
				console.log('[TauriPushConfig] 시뮬레이션 알림:', { title, options });
				return true;
			}
			
			default:
				return false;
		}
	} catch (error) {
		console.error('[TauriPushConfig] 알림 표시 실패:', error);
		return false;
	}
}

/**
 * 환경별 초기화
 */
export async function initializePushNotificationsByEnvironment(): Promise<void> {
	logEnvironmentInfo();
	
	const strategy = getPushNotificationStrategy();
	const config = getTauriPushConfig();
	
	console.log(`[TauriPushConfig] ${strategy} 전략으로 푸시 알림 초기화`);
	
	// 권한 요청
	const permission = await requestNotificationPermissionByStrategy();
	console.log('[TauriPushConfig] 알림 권한:', permission);
	
	if (permission !== 'granted') {
		console.warn('[TauriPushConfig] 알림 권한이 거부되었습니다.');
	}
	
	// 테스트 알림 (개발 환경에서만)
	if (config.debugMode && permission === 'granted') {
		setTimeout(async () => {
			await showNotificationByStrategy('CNSPROWMS', {
				body: `${strategy} 전략으로 푸시 알림이 초기화되었습니다.`,
				icon: '/favicon.png'
			});
		}, 2000);
	}
}
