/**
 * Tauri 환경에서의 푸시 알림 설정 관리
 */

export interface TauriPushConfig {
	useNativeNotifications: boolean;
	fallbackToServiceWorker: boolean;
	enablePusherBeams: boolean;
	debugMode: boolean;
}

/**
 * Tauri 환경 감지 (개선된 버전)
 */
export function isTauriEnvironment(): boolean {
	if (typeof window === 'undefined') {
		return false;
	}

	// 1. __TAURI__ 객체 확인 (가장 확실한 방법)
	if (window.__TAURI__ !== undefined) {
		return true;
	}

	// 2. User Agent 확인 (Tauri 앱은 특별한 User Agent를 가짐)
	if (navigator.userAgent.includes('Tauri')) {
		return true;
	}

	// 3. 프로토콜 확인 (Tauri 앱은 tauri:// 프로토콜 사용)
	if (window.location.protocol === 'tauri:') {
		return true;
	}

	// 4. IPC 엔드포인트 확인 (Tauri 특유의 IPC 주소)
	if (window.location.hostname === 'ipc.localhost' || window.location.hostname.includes('tauri.localhost')) {
		return true;
	}

	// 5. Tauri API 존재 확인
	try {
		// @ts-ignore
		if (window.__TAURI_INTERNALS__ !== undefined) {
			return true;
		}
	} catch (e) {
		// 무시
	}

	// 6. 개발 환경에서 추가 확인
	if (import.meta.env.DEV) {
		// 개발 환경에서는 포트 번호로 추가 확인
		const port = window.location.port;
		if (port && (port === '1420' || port.startsWith('142'))) {
			// Tauri 기본 개발 포트
			return true;
		}
	}

	return false;
}

/**
 * 개발 환경 감지
 */
export function isDevelopmentEnvironment(): boolean {
	return import.meta.env.DEV || import.meta.env.VITE_NODE_ENV === 'development';
}

/**
 * Tauri 푸시 알림 설정 가져오기
 */
export function getTauriPushConfig(): TauriPushConfig {
	const isTauri = isTauriEnvironment();
	const isDev = isDevelopmentEnvironment();

	return {
		// Tauri 환경에서는 네이티브 알림 우선 사용
		useNativeNotifications: isTauri,
		
		// Tauri 환경에서 네이티브 알림 실패 시 Service Worker로 폴백
		fallbackToServiceWorker: isTauri && isDev,
		
		// 개발 환경에서는 Pusher Beams 시뮬레이션 모드
		enablePusherBeams: !isTauri || isDev,
		
		// 개발 환경에서 디버그 모드 활성화
		debugMode: isDev
	};
}

/**
 * 푸시 알림 전략 결정 (개선된 버전)
 */
export function getPushNotificationStrategy(): 'native' | 'service-worker' | 'simulation' {
	const isTauri = isTauriEnvironment();
	const isDev = isDevelopmentEnvironment();

	console.log('[TauriPushConfig] 전략 결정 - Tauri 환경:', isTauri, '개발 환경:', isDev);

	// 1. Tauri 환경에서는 항상 네이티브 알림 우선
	if (isTauri) {
		console.log('[TauriPushConfig] Tauri 환경 감지 - 네이티브 알림 전략 선택');
		return 'native';
	}

	// 2. 웹 환경에서 Service Worker 지원 확인
	if (typeof window !== 'undefined' && 'serviceWorker' in navigator && 'PushManager' in window) {
		console.log('[TauriPushConfig] 웹 환경 - Service Worker 전략 선택');
		return 'service-worker';
	}

	// 3. 폴백 - 시뮬레이션 모드
	console.log('[TauriPushConfig] 폴백 - 시뮬레이션 전략 선택');
	return 'simulation';
}

/**
 * 환경별 로깅
 */
export function logEnvironmentInfo(): void {
	const config = getTauriPushConfig();
	const strategy = getPushNotificationStrategy();
	
	console.group('[TauriPushConfig] 환경 정보');
	console.log('Tauri 환경:', isTauriEnvironment());
	console.log('개발 환경:', isDevelopmentEnvironment());
	console.log('푸시 전략:', strategy);
	console.log('설정:', config);
	console.groupEnd();
}

/**
 * 권한 요청 전략
 */
export async function requestNotificationPermissionByStrategy(): Promise<'granted' | 'denied' | 'default'> {
	const strategy = getPushNotificationStrategy();
	
	try {
		switch (strategy) {
			case 'native': {
				try {
					// Tauri 네이티브 알림 권한 요청
					console.log('[TauriPushConfig] Tauri 네이티브 알림 권한 확인 중...');

					const { isPermissionGranted, requestPermission } = await import('@tauri-apps/plugin-notification');

					// 현재 권한 상태 확인
					const hasPermission = await isPermissionGranted();
					console.log('[TauriPushConfig] 현재 권한 상태:', hasPermission);

					if (hasPermission) {
						return 'granted';
					}

					// 권한 요청
					console.log('[TauriPushConfig] 알림 권한 요청 중...');
					const permission = await requestPermission();
					console.log('[TauriPushConfig] 권한 요청 결과:', permission);

					return permission === 'granted' ? 'granted' : 'denied';
				} catch (error) {
					console.error('[TauriPushConfig] Tauri 네이티브 알림 권한 요청 실패:', error);

					// IPC 통신 실패 시 시뮬레이션으로 폴백
					console.warn('[TauriPushConfig] IPC 통신 실패 - 시뮬레이션 모드로 폴백');
					return 'granted'; // 시뮬레이션에서는 항상 허용
				}
			}
			
			case 'service-worker': {
				// 웹 푸시 알림 권한 요청
				if ('Notification' in window) {
					const permission = await Notification.requestPermission();
					return permission;
				}
				return 'denied';
			}
			
			case 'simulation': {
				// 시뮬레이션 모드에서는 항상 허용
				console.log('[TauriPushConfig] 시뮬레이션 모드 - 권한 허용');
				return 'granted';
			}
			
			default:
				return 'denied';
		}
	} catch (error) {
		console.error('[TauriPushConfig] 권한 요청 실패:', error);
		return 'denied';
	}
}

/**
 * 알림 표시 전략
 */
export async function showNotificationByStrategy(
	title: string, 
	options: NotificationOptions & { body?: string }
): Promise<boolean> {
	const strategy = getPushNotificationStrategy();
	
	try {
		switch (strategy) {
			case 'native': {
				try {
					// Tauri 네이티브 알림
					console.log('[TauriPushConfig] Tauri 네이티브 알림 표시 시도:', { title, body: options.body });

					const { sendNotification } = await import('@tauri-apps/plugin-notification');

					await sendNotification({
						title,
						body: options.body || '',
						icon: options.icon || '/favicon.png'
					});

					console.log('[TauriPushConfig] 네이티브 알림 표시 완료');
					return true;
				} catch (error) {
					console.error('[TauriPushConfig] Tauri 네이티브 알림 표시 실패:', error);

					// IPC 통신 실패 시 브라우저 알림으로 폴백
					if ('Notification' in window && Notification.permission === 'granted') {
						console.log('[TauriPushConfig] 브라우저 알림으로 폴백');
						new Notification(title, options);
						return true;
					}

					// 모든 방법 실패 시 콘솔 로그
					console.warn('[TauriPushConfig] 모든 알림 방법 실패 - 콘솔 로그로 표시');
					console.log(`🔔 알림: ${title} - ${options.body}`);
					return false;
				}
			}
			
			case 'service-worker': {
				// 웹 알림
				if ('Notification' in window && Notification.permission === 'granted') {
					new Notification(title, options);
					console.log('[TauriPushConfig] 웹 알림 표시 완료');
					return true;
				}
				return false;
			}
			
			case 'simulation': {
				// 시뮬레이션 모드
				console.log('[TauriPushConfig] 시뮬레이션 알림:', { title, options });
				return true;
			}
			
			default:
				return false;
		}
	} catch (error) {
		console.error('[TauriPushConfig] 알림 표시 실패:', error);
		return false;
	}
}

/**
 * 환경별 초기화 (개선된 버전)
 */
export async function initializePushNotificationsByEnvironment(): Promise<void> {
	console.log('[TauriPushConfig] 푸시 알림 초기화 시작');

	// 환경 정보 로깅
	logEnvironmentInfo();

	const strategy = getPushNotificationStrategy();
	const config = getTauriPushConfig();
	const isTauri = isTauriEnvironment();

	console.log(`[TauriPushConfig] 선택된 전략: ${strategy}`);
	console.log(`[TauriPushConfig] Tauri 환경: ${isTauri}`);

	try {
		// 권한 요청
		console.log('[TauriPushConfig] 알림 권한 요청 중...');
		const permission = await requestNotificationPermissionByStrategy();
		console.log('[TauriPushConfig] 알림 권한 결과:', permission);

		if (permission !== 'granted') {
			console.warn('[TauriPushConfig] 알림 권한이 거부되었습니다.');

			// Tauri 환경에서 권한 거부 시 추가 안내
			if (isTauri) {
				console.log('[TauriPushConfig] Tauri 앱에서 알림을 사용하려면 시스템 설정에서 알림을 허용해주세요.');
			}
		}

		// 초기화 완료 알림 (권한이 있을 때만)
		if (permission === 'granted') {
			// 즉시 테스트 알림 표시 (개발 환경)
			if (config.debugMode) {
				console.log('[TauriPushConfig] 테스트 알림 표시 중...');

				const success = await showNotificationByStrategy('CNSPROWMS 초기화 완료', {
					body: `${strategy} 전략으로 푸시 알림이 성공적으로 초기화되었습니다.`,
					icon: '/favicon.png'
				});

				if (success) {
					console.log('[TauriPushConfig] 테스트 알림 표시 성공');
				} else {
					console.warn('[TauriPushConfig] 테스트 알림 표시 실패');
				}
			}

			// 프로덕션 환경에서는 간단한 확인 알림만
			if (!config.debugMode && isTauri) {
				setTimeout(async () => {
					await showNotificationByStrategy('CNSPROWMS', {
						body: '알림이 활성화되었습니다.',
						icon: '/favicon.png'
					});
				}, 1000);
			}
		}

		console.log('[TauriPushConfig] 푸시 알림 초기화 완료');

	} catch (error) {
		console.error('[TauriPushConfig] 푸시 알림 초기화 실패:', error);

		// 초기화 실패 시에도 기본 기능은 동작하도록 함
		console.log('[TauriPushConfig] 기본 모드로 계속 진행합니다.');
	}
}
