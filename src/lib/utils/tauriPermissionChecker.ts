/**
 * Tauri v2 푸시 알림 권한 확인 및 진단 도구
 */

import { getCurrentPlatform, isDesktop, isAndroid } from '$lib/services/platformService';

/**
 * 권한 확인 결과
 */
export interface PermissionCheckResult {
	platform: string;
	isSupported: boolean;
	permissions: {
		notification: {
			available: boolean;
			granted: boolean;
			error?: string;
		};
		remotePush?: {
			available: boolean;
			granted: boolean;
			error?: string;
		};
		webview: {
			available: boolean;
			error?: string;
		};
		store: {
			available: boolean;
			error?: string;
		};
	};
	recommendations: string[];
	errors: string[];
}

/**
 * Tauri 알림 권한 확인
 */
async function checkNotificationPermission(): Promise<{
	available: boolean;
	granted: boolean;
	error?: string;
}> {
	try {
		// Tauri 알림 플러그인 사용 가능 여부 확인
		if (typeof window !== 'undefined' && (window as any).__TAURI__) {
			const { isPermissionGranted, requestPermission } = await import('@tauri-apps/plugin-notification');
			
			const isGranted = await isPermissionGranted();
			return {
				available: true,
				granted: isGranted
			};
		} else {
			// 브라우저 환경에서는 Notification API 확인
			if ('Notification' in window) {
				return {
					available: true,
					granted: Notification.permission === 'granted'
				};
			} else {
				return {
					available: false,
					granted: false,
					error: 'Notification API를 지원하지 않는 환경입니다.'
				};
			}
		}
	} catch (error) {
		return {
			available: false,
			granted: false,
			error: `알림 권한 확인 실패: ${error}`
		};
	}
}

/**
 * Tauri 원격 푸시 권한 확인 (안드로이드 전용)
 */
async function checkRemotePushPermission(): Promise<{
	available: boolean;
	granted: boolean;
	error?: string;
}> {
	try {
		if (!isAndroid()) {
			return {
				available: false,
				granted: false,
				error: '안드로이드 플랫폼이 아닙니다.'
			};
		}

		// Tauri 원격 푸시 플러그인 확인
		const { requestPermission } = await import('tauri-plugin-remote-push-api');
		
		// 권한 요청을 통해 현재 상태 확인
		const permission = await requestPermission();
		
		return {
			available: true,
			granted: permission.granted
		};
	} catch (error) {
		return {
			available: false,
			granted: false,
			error: `원격 푸시 권한 확인 실패: ${error}`
		};
	}
}

/**
 * 웹뷰 기능 확인
 */
async function checkWebviewCapability(): Promise<{
	available: boolean;
	error?: string;
}> {
	try {
		// Tauri 환경 확인
		if (typeof window !== 'undefined' && (window as any).__TAURI__) {
			// Service Worker 지원 확인
			const hasServiceWorker = 'serviceWorker' in navigator;
			const hasPushManager = 'PushManager' in window;
			
			if (!hasServiceWorker) {
				return {
					available: false,
					error: 'Service Worker를 지원하지 않습니다.'
				};
			}
			
			if (!hasPushManager) {
				return {
					available: false,
					error: 'Push Manager를 지원하지 않습니다.'
				};
			}
			
			return {
				available: true
			};
		} else {
			return {
				available: false,
				error: 'Tauri 환경이 아닙니다.'
			};
		}
	} catch (error) {
		return {
			available: false,
			error: `웹뷰 기능 확인 실패: ${error}`
		};
	}
}

/**
 * 스토어 기능 확인
 */
async function checkStoreCapability(): Promise<{
	available: boolean;
	error?: string;
}> {
	try {
		// Tauri Store 플러그인 확인
		const { Store } = await import('@tauri-apps/plugin-store');
		
		// 테스트 스토어 생성
		const testStore = new Store('permission-test.json');
		await testStore.set('test', 'value');
		await testStore.save();
		await testStore.delete('test');
		
		return {
			available: true
		};
	} catch (error) {
		return {
			available: false,
			error: `스토어 기능 확인 실패: ${error}`
		};
	}
}

/**
 * 종합 권한 확인
 */
export async function checkTauriPermissions(): Promise<PermissionCheckResult> {
	const platform = getCurrentPlatform();
	const result: PermissionCheckResult = {
		platform,
		isSupported: isDesktop() || isAndroid(),
		permissions: {
			notification: { available: false, granted: false },
			webview: { available: false },
			store: { available: false }
		},
		recommendations: [],
		errors: []
	};

	// 알림 권한 확인
	result.permissions.notification = await checkNotificationPermission();

	// 안드로이드인 경우 원격 푸시 권한 확인
	if (isAndroid()) {
		result.permissions.remotePush = await checkRemotePushPermission();
	}

	// 웹뷰 기능 확인
	result.permissions.webview = await checkWebviewCapability();

	// 스토어 기능 확인
	result.permissions.store = await checkStoreCapability();

	// 권장사항 생성
	generateRecommendations(result);

	return result;
}

/**
 * 권장사항 생성
 */
function generateRecommendations(result: PermissionCheckResult): void {
	const recommendations = [];
	const errors = [];

	// 플랫폼 지원 확인
	if (!result.isSupported) {
		errors.push(`지원되지 않는 플랫폼입니다: ${result.platform}`);
		recommendations.push('데스크탑(Windows/macOS/Linux) 또는 안드로이드 환경에서 실행하세요.');
	}

	// 알림 권한 확인
	if (!result.permissions.notification.available) {
		errors.push('알림 기능을 사용할 수 없습니다.');
		recommendations.push('Tauri 알림 플러그인이 올바르게 설치되었는지 확인하세요.');
	} else if (!result.permissions.notification.granted) {
		recommendations.push('알림 권한을 허용해주세요.');
	}

	// 원격 푸시 권한 확인 (안드로이드)
	if (result.permissions.remotePush) {
		if (!result.permissions.remotePush.available) {
			errors.push('원격 푸시 기능을 사용할 수 없습니다.');
			recommendations.push('Tauri 원격 푸시 플러그인이 올바르게 설치되었는지 확인하세요.');
		} else if (!result.permissions.remotePush.granted) {
			recommendations.push('원격 푸시 권한을 허용해주세요.');
		}
	}

	// 웹뷰 기능 확인
	if (!result.permissions.webview.available) {
		errors.push('웹뷰 기능을 사용할 수 없습니다.');
		recommendations.push('최신 브라우저 엔진을 지원하는 환경에서 실행하세요.');
	}

	// 스토어 기능 확인
	if (!result.permissions.store.available) {
		errors.push('스토어 기능을 사용할 수 없습니다.');
		recommendations.push('Tauri 스토어 플러그인이 올바르게 설치되었는지 확인하세요.');
	}

	// 에러 메시지 수집
	Object.values(result.permissions).forEach(permission => {
		if (permission.error) {
			errors.push(permission.error);
		}
	});

	result.recommendations = recommendations;
	result.errors = errors;
}

/**
 * 권한 확인 결과 출력
 */
export async function logPermissionStatus(): Promise<void> {
	if (import.meta.env.VITE_NODE_ENV !== 'development') {
		return;
	}

	const result = await checkTauriPermissions();
	
	console.group('🔐 Tauri 푸시 알림 권한 확인');
	
	console.group('📊 플랫폼 정보');
	console.log('플랫폼:', result.platform);
	console.log('지원 여부:', result.isSupported);
	console.groupEnd();
	
	console.group('🔔 권한 상태');
	console.log('알림:', result.permissions.notification);
	if (result.permissions.remotePush) {
		console.log('원격 푸시:', result.permissions.remotePush);
	}
	console.log('웹뷰:', result.permissions.webview);
	console.log('스토어:', result.permissions.store);
	console.groupEnd();
	
	if (result.errors.length > 0) {
		console.group('❌ 에러');
		result.errors.forEach(error => console.error(error));
		console.groupEnd();
	}
	
	if (result.recommendations.length > 0) {
		console.group('💡 권장사항');
		result.recommendations.forEach(rec => console.log(rec));
		console.groupEnd();
	}
	
	console.groupEnd();
}

/**
 * 개발 환경에서 전역 함수 등록
 */
export function registerPermissionCheckFunctions(): void {
	if (import.meta.env.VITE_NODE_ENV !== 'development' || typeof window === 'undefined') {
		return;
	}

	(window as any).tauriPermissions = {
		check: checkTauriPermissions,
		log: logPermissionStatus
	};

	console.log('🔐 Tauri 권한 확인 함수 등록 완료:');
	console.log('- window.tauriPermissions.check()');
	console.log('- window.tauriPermissions.log()');
}
