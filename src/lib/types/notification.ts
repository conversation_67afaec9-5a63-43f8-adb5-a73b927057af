import { data } from 'autoprefixer';

/**
 * 알림 우선순위
 */
export type NotificationPriority = 'low' | 'normal' | 'high' | 'urgent';

/**
 * 푸시 알림 데이터 인터페이스
 * 기존 Pusher 메시지 포맷과 호환되는 구조로 설계
 * 서버에서 이미 필터링되어 전송되므로 클라이언트에서는 우선순위와 읽음 상태에 집중
 * action_url/redirect 필드는 사용하지 않음 (요구사항에 따라 제거됨)
 */
export interface NotificationData {
	// 기본 필드
	id: string;
	title: string;
	body: string;
	icon?: string;
	content: string;
	expire_day: string; // ISO 8601 형식 (YYYY-MM-DD)

	// 메타데이터
	created_at: string; // ISO 8601 형식
	read: boolean;

	// 기존 Pusher 호환성 필드
	success?: boolean;
	message?: string;

	// 확장 필드
	priority?: NotificationPriority;
	category?: string;
	image_url?: string;
	deletable?: boolean; // 삭제 가능 여부
}

/**
 * 새로운 알림 생성 시 사용하는 입력 데이터
 * id, created_at, read 필드는 자동 생성되므로 제외
 * action_url/redirect 필드는 사용하지 않음 (요구사항에 따라 제거됨)
 */
export interface CreateNotificationData {
	content: string;
	expire_day: string;
	success?: boolean;
	message?: string;
	priority?: NotificationPriority;
	category?: string;
	image_url?: string;
	deletable?: boolean; // 삭제 가능 여부
}

/**
 * 알림 업데이트 시 사용하는 데이터
 * 부분 업데이트를 위해 모든 필드를 선택적으로 만듦
 * action_url/redirect 필드는 사용하지 않음 (요구사항에 따라 제거됨)
 */
export interface UpdateNotificationData {
	content?: string;
	expire_day?: string;
	read?: boolean;
	success?: boolean;
	message?: string;
	priority?: NotificationPriority;
	category?: string;
	image_url?: string;
	deletable?: boolean; // 삭제 가능 여부
}

/**
 * 알림 필터링 옵션
 */
export interface NotificationFilter {
	read?: boolean;
	priority?: NotificationPriority;
	category?: string;
	date_from?: string; // 시작 날짜 (ISO 8601)
	date_to?: string; // 종료 날짜 (ISO 8601)
}

/**
 * 알림 통계 정보
 */
export interface NotificationStats {
	total: number;
	unread: number;
	by_priority: Record<NotificationPriority, number>;
	expired: number;
}

/**
 * 기존 Pusher 메시지와의 호환성을 위한 타입
 * 기존 코드에서 사용하던 메시지 포맷
 */
export interface LegacyPusherMessage {
	success?: boolean;
	message?: string;
	content?: string;
	expire_day?: string;
}

/**
 * Pusher 메시지를 NotificationData로 변환하는 유틸리티 함수 타입
 */
export type PusherMessageConverter = (
	message: LegacyPusherMessage,
	priority?: NotificationPriority
) => CreateNotificationData;

/**
 * 알림 데이터 검증 함수들
 */
export const NotificationValidators = {
	/**
	 * 알림 ID가 유효한지 검증
	 */
	isValidId: (id: string): boolean => {
		return id.length > 0;
	},

	/**
	 * 만료일이 유효한 ISO 8601 형식인지 검증
	 */
	isValidExpireDay: (expireDay: string): boolean => {
		const date = new Date(expireDay);
		return !isNaN(date.getTime()) && !!expireDay.match(/^\d{4}-\d{2}-\d{2}$/);
	},

	/**
	 * 우선순위가 유효한지 검증
	 */
	isValidPriority: (priority: string): priority is NotificationPriority => {
		return ['low', 'normal', 'high', 'urgent'].includes(priority);
	},

	/**
	 * 전체 알림 데이터가 유효한지 검증
	 */
	isValidNotificationData: (data: Partial<NotificationData>): data is NotificationData => {
		return !!(
			data.id &&
			data.content &&
			data.expire_day &&
			data.created_at &&
			typeof data.read === 'boolean' &&
			NotificationValidators.isValidId(data.id) &&
			NotificationValidators.isValidExpireDay(data.expire_day)
		);
	}
};

/**
 * 알림 유틸리티 함수들
 */
export const NotificationUtils = {
	/**
	 * 새로운 알림 ID 생성
	 */
	generateId: (): string => {
		return `notification_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
	},

	/**
	 * 현재 시간을 ISO 8601 형식으로 반환
	 */
	getCurrentTimestamp: (): string => {
		return new Date().toISOString();
	},

	/**
	 * 기본 만료일 계산 (6개월 후)
	 * Requirements: 3.3 - 만료일이 설정되어 있지 않으면 6개월간 표시
	 */
	getDefaultExpireDay: (): string => {
		const date = new Date();
		date.setMonth(date.getMonth() + 6);
		return date.toISOString().split('T')[0]; // YYYY-MM-DD 형식
	},

	/**
	 * 알림이 만료되었는지 확인
	 */
	isExpired: (notification: NotificationData): boolean => {
		const expireDate = new Date(notification.expire_day);
		const now = new Date();
		now.setHours(23, 59, 59, 999); // 당일 끝까지 유효하도록
		return expireDate < now;
	},

	/**
	 * 기존 Pusher 메시지를 NotificationData로 변환
	 * 기존 코드와의 호환성 유지
	 */
	convertFromPusherMessage: (
		message: LegacyPusherMessage,
		priority: NotificationPriority = 'normal'
	): CreateNotificationData => {
		return {
			content: message.content || message.message || '',
			expire_day: message.expire_day || NotificationUtils.getDefaultExpireDay(),
			success: message.success,
			message: message.message,
			priority
		};
	},

	/**
	 * CreateNotificationData를 완전한 NotificationData로 변환
	 */
	createNotification: (data: CreateNotificationData): NotificationData => {
		return {
			body: '', title: '',
			id: NotificationUtils.generateId(),
			created_at: NotificationUtils.getCurrentTimestamp(),
			read: false,
			...data
		};
	}
};

/**
 * 알림 상수들
 */
export const NotificationConstants = {
	// 기본 설정값
	DEFAULT_EXPIRE_MONTHS: 6,
	DEFAULT_PRIORITY: 'normal' as NotificationPriority,

	// IndexedDB 관련
	DB_NAME: 'EmployeeDB',
	DB_VERSION: 3,
	STORE_NAME: 'push_notifications',

	// UI 관련
	MAX_CONTENT_LENGTH: 200,
	ROTATION_INTERVAL: 3000, // 3초
	MAX_DISPLAY_LENGTH: 50,

	// 성능 관련
	MAX_STORED_NOTIFICATIONS: 100,
	CLEANUP_INTERVAL: 24 * 60 * 60 * 1000 // 24시간
} as const;
