#!/usr/bin/env node

/**
 * Tauri 푸시 알림 테스트 스크립트
 * 개발 환경에서 푸시 알림 기능을 테스트하기 위한 스크립트
 */
// import * as fs from 'fs';
// import * as path from 'path';
// import { execSync } from 'child_process';

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🔔 Tauri 푸시 알림 테스트 시작\n');

// 1. 환경 변수 확인
console.log('1. 환경 변수 확인...');
const envPath = path.join(__dirname, '.', '.env');
if (fs.existsSync(envPath)) {
    const envContent = fs.readFileSync(envPath, 'utf8');
    const requiredVars = [
        'VITE_PUSHER_BEAMS_INSTANCE_ID',
        'VITE_FIREBASE_SENDER_ID',
        'VITE_FIREBASE_PROJECT_ID',
        'VITE_FIREBASE_API_KEY'
    ];
    
    const missingVars = requiredVars.filter(varName => !envContent.includes(varName));
    
    if (missingVars.length > 0) {
        console.error('❌ 누락된 환경 변수:', missingVars.join(', '));
        process.exit(1);
    }
    
    console.log('✅ 모든 필수 환경 변수가 설정되어 있습니다.');
} else {
    console.error('❌ .env 파일을 찾을 수 없습니다.');
    process.exit(1);
}

// 2. Tauri 설정 확인
console.log('\n2. Tauri 설정 확인...');
const tauriConfigPath = path.join(__dirname, '.', 'src-tauri', 'tauri.conf.json');
if (fs.existsSync(tauriConfigPath)) {
    const tauriConfig = JSON.parse(fs.readFileSync(tauriConfigPath, 'utf8'));
    
    // 권한 확인
    const capabilities = [
        path.join(__dirname, '..', 'src-tauri', 'capabilities', 'default.json'),
        path.join(__dirname, '..', 'src-tauri', 'capabilities', 'desktop.json')
    ];
    
    let hasNotificationPermission = false;
    
    capabilities.forEach(capPath => {
        if (fs.existsSync(capPath)) {
            const capConfig = JSON.parse(fs.readFileSync(capPath, 'utf8'));
            if (capConfig.permissions && capConfig.permissions.includes('notification:default')) {
                hasNotificationPermission = true;
            }
        }
    });
    
    if (hasNotificationPermission) {
        console.log('✅ Tauri 알림 권한이 설정되어 있습니다.');
    } else {
        console.warn('⚠️  Tauri 알림 권한이 설정되지 않았을 수 있습니다.');
    }
    
    // CSP 확인
    if (tauriConfig.app && tauriConfig.app.security && tauriConfig.app.security.csp) {
        console.log('✅ CSP가 설정되어 있습니다.');
    } else {
        console.warn('⚠️  CSP가 설정되지 않았습니다.');
    }
    
} else {
    console.error('❌ tauri.conf.json 파일을 찾을 수 없습니다.');
    process.exit(1);
}

// 3. 의존성 확인
console.log('\n3. 의존성 확인...');
try {
    const packageJsonPath = path.join(__dirname, '.', 'package.json');
    const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
    
    const requiredDeps = [
        '@tauri-apps/plugin-notification',
        '@pusher/push-notifications-web'
    ];
    
    const allDeps = { ...packageJson.dependencies, ...packageJson.devDependencies };
    const missingDeps = requiredDeps.filter(dep => !allDeps[dep]);
    
    if (missingDeps.length > 0) {
        console.error('❌ 누락된 의존성:', missingDeps.join(', '));
        console.log('다음 명령어로 설치하세요:');
        console.log(`npm install ${missingDeps.join(' ')}`);
        process.exit(1);
    }
    
    console.log('✅ 모든 필수 의존성이 설치되어 있습니다.');
} catch (error) {
    console.error('❌ package.json 확인 실패:', error.message);
    process.exit(1);
}

// 4. 빌드 테스트
console.log('\n4. 개발 빌드 테스트...');
try {
    console.log('프론트엔드 빌드 중...');
    execSync('pnpm run build', { stdio: 'pipe' });
    console.log('✅ 프론트엔드 빌드 성공');
    
    console.log('Tauri 개발 빌드 중...');
    execSync('.\\build.ps1', { stdio: 'pipe', cwd: path.join(__dirname, '.') });
    console.log('✅ Tauri 개발 빌드 성공');
    
} catch (error) {
    console.error('❌ 빌드 실패:', error.message);
    console.log('\n빌드 로그를 확인하세요:');
    console.log('pnpm run build');
    console.log('pnpm tauri build --debug');
    process.exit(1);
}

// 5. 테스트 완료
console.log('\n🎉 모든 테스트가 완료되었습니다!');
console.log('\n다음 단계:');
console.log('1. pnpm tauri dev 로 개발 서버 시작');
console.log('2. 앱에서 알림 권한 요청 확인');
console.log('3. 브라우저 개발자 도구에서 푸시 알림 로그 확인');
console.log('4. Tauri 네이티브 알림 테스트');

console.log('\n디버깅 팁:');
console.log('- 브라우저 콘솔에서 [TauriPushConfig] 로그 확인');
console.log('- Service Worker 등록 상태 확인');
console.log('- Tauri 개발자 도구에서 네이티브 API 호출 확인');
