# Tauri v2 푸시 알림 권한 분석 및 설정 가이드

## 현재 권한 설정 상태

### ✅ 올바르게 설정된 권한들

#### 1. **데스크탑 플랫폼 권한** (`src-tauri/capabilities/default.json`)
```json
{
  "platforms": ["macOS", "windows", "linux"],
  "permissions": [
    "notification:default",           // ✅ 브라우저 알림 권한
    "notification:allow-request-permission", // ✅ 권한 요청
    "notification:allow-show",        // ✅ 알림 표시
    "notification:allow-is-permission-granted", // ✅ 권한 상태 확인
    "core:webview:allow-create-webview-window", // ✅ 웹뷰 생성
    "core:webview:allow-webview-close",         // ✅ 웹뷰 관리
    "core:webview:allow-internal-toggle-devtools", // ✅ 개발자 도구
    "store:default",                  // ✅ 로컬 스토리지
    "os:default"                      // ✅ OS 정보 접근
  ]
}
```

#### 2. **모바일 플랫폼 권한** (`src-tauri/capabilities/mobile.json`)
```json
{
  "platforms": ["iOS", "android"],
  "permissions": [
    "notification:default",           // ✅ 로컬 알림
    "notification:allow-request-permission", // ✅ 권한 요청
    "notification:allow-show",        // ✅ 알림 표시
    "notification:allow-is-permission-granted", // ✅ 권한 상태 확인
    "remote-push:default",           // ✅ 원격 푸시 알림 (FCM)
    "remote-push:allow-request-permission", // ✅ 원격 푸시 권한 요청
    "remote-push:allow-register",    // ✅ 디바이스 등록
    "remote-push:allow-unregister",  // ✅ 디바이스 등록 해제
    "remote-push:allow-get-token",   // ✅ FCM 토큰 획득
    "store:default",                 // ✅ 로컬 스토리지
    "os:default"                     // ✅ OS 정보 접근
  ]
}
```

#### 3. **안드로이드 매니페스트 권한** (`AndroidManifest.xml`)
```xml
<uses-permission android:name="android.permission.INTERNET" />
<uses-permission android:name="android.permission.WAKE_LOCK" />
<uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
```

#### 4. **Cargo.toml 의존성**
```toml
# 공통 플러그인
tauri-plugin-notification = "2"        # ✅ 로컬 알림
tauri-plugin-store = "2"               # ✅ 데이터 저장

# 안드로이드 전용
[target.'cfg(target_os = "android")'.dependencies]
tauri-plugin-remote-push = "1"         # ✅ FCM 지원
```

#### 5. **Firebase/FCM 설정**
- ✅ `google-services.json` 파일 존재
- ✅ FCM Service 등록 (`app.tauri.remotepush.FCMService`)
- ✅ Firebase 의존성 (`firebase-messaging:24.1.0`)
- ✅ Pusher Beams Android SDK (`push-notifications-android:1.9.0`)

### ⚠️ 개선이 필요한 부분

#### 1. **환경 변수 불일치**
**문제**: `tauri.conf.json`과 `.env` 파일의 Firebase Sender ID가 다름
```json
// tauri.conf.json
"remote-push": {
  "senderId": "531915626888"  // 프로덕션 ID
}
```
```env
// .env (현재 개발 환경)
VITE_FIREBASE_SENDER_ID=1065183976379  // 개발 ID
```

#### 2. **Service Worker 권한 부족**
**문제**: 데스크탑에서 Service Worker 등록 시 권한 문제 발생 가능

## 권한 문제 해결 방안

### 1. 환경별 Firebase 설정 통일

#### A. 개발 환경용 설정 (현재 .env 기준)
```json
// src-tauri/tauri.conf.json 수정
"plugins": {
  "remote-push": {
    "senderId": "1065183976379"  // 개발 환경 ID로 변경
  }
}
```

#### B. 또는 프로덕션 환경용 설정
```env
# .env 파일 수정
VITE_FIREBASE_SENDER_ID=531915626888  # 프로덕션 ID로 변경
```

### 2. 웹뷰 보안 정책 완화

#### CSP (Content Security Policy) 설정
```json
// src-tauri/tauri.conf.json
"app": {
  "security": {
    "csp": "default-src 'self' 'unsafe-inline' 'unsafe-eval' data: https:; connect-src 'self' https: wss: ws:;"
  }
}
```

### 3. 추가 권한 설정

#### 데스크탑 권한 강화
```json
// src-tauri/capabilities/default.json에 추가
{
  "permissions": [
    "notification:default",
    "notification:allow-request-permission",
    "notification:allow-show",
    "notification:allow-cancel",
    "core:webview:allow-internal-toggle-devtools",
    "core:window:allow-set-focus"
  ]
}
```

## 플랫폼별 푸시 알림 구현 방식

### 🖥️ **데스크탑 (Windows/macOS/Linux)**
1. **Pusher Beams Web SDK** 사용
2. **브라우저 Notification API** 활용
3. **Service Worker** 기반 백그라운드 처리
4. **WebView** 내에서 실행

**필요 권한:**
- `notification:default` ✅
- `core:webview:*` ✅
- `store:default` ✅

### 📱 **안드로이드**
1. **Firebase Cloud Messaging (FCM)** 사용
2. **Pusher Beams Android SDK** 활용
3. **네이티브 알림** 시스템 사용
4. **백그라운드 서비스** 지원

**필요 권한:**
- `remote-push:default` ✅
- `notification:default` ✅
- `android.permission.INTERNET` ✅
- `com.google.android.c2dm.permission.RECEIVE` ✅

### 🔧 추가된 권한 개선사항

#### 1. **세분화된 알림 권한**
- `notification:allow-request-permission` - 권한 요청 허용
- `notification:allow-show` - 알림 표시 허용
- `notification:allow-is-permission-granted` - 권한 상태 확인 허용

#### 2. **세분화된 원격 푸시 권한** (안드로이드)
- `remote-push:allow-request-permission` - 원격 푸시 권한 요청
- `remote-push:allow-register` - 디바이스 등록
- `remote-push:allow-unregister` - 디바이스 등록 해제
- `remote-push:allow-get-token` - FCM 토큰 획득

#### 3. **추가 윈도우 관리 권한**
- `core:window:allow-minimize` - 창 최소화
- `core:window:allow-maximize` - 창 최대화
- `core:window:allow-show/hide` - 창 표시/숨김
- `core:window:allow-set-focus` - 창 포커스 설정

## 플랫폼별 푸시 알림 구현 방식

### 🖥️ **데스크탑 (Windows/macOS/Linux)**
1. **Pusher Beams Web SDK** 사용
2. **브라우저 Notification API** 활용
3. **Service Worker** 기반 백그라운드 처리
4. **WebView** 내에서 실행

**필요 권한:**
- `notification:default` ✅
- `core:webview:*` ✅
- `store:default` ✅

### 📱 **안드로이드**
1. **Firebase Cloud Messaging (FCM)** 사용
2. **Pusher Beams Android SDK** 활용
3. **네이티브 알림** 시스템 사용
4. **백그라운드 서비스** 지원

**필요 권한:**
- `remote-push:default` ✅
- `notification:default` ✅
- `android.permission.INTERNET` ✅
- `com.google.android.c2dm.permission.RECEIVE` ✅

## 현재 발생 중인 문제 분석

### 문제 1: Push Service Manager 초기화 실패
**원인**: 권한 문제가 아닌 **초기화 순서** 및 **환경 설정** 문제
- Pusher Beams Instance ID 설정 ✅
- Firebase 설정 ✅
- 권한 설정 ✅

**실제 원인**: 
1. 개발 환경에서 HTTP vs HTTPS 프로토콜 제약
2. Service Worker 등록 실패
3. 환경 변수 불일치

### 문제 2: Service Worker 등록 권한 거부
**원인**: 브라우저 보안 정책, 개발 환경 제약
**해결책**: 이미 시뮬레이션 모드로 해결됨 ✅

## 권한 확인 및 진단 도구

### 새로운 디버깅 도구 추가
```javascript
// Tauri 권한 상태 확인
await window.tauriPermissions.check();

// 권한 상태 로그 출력
await window.tauriPermissions.log();

// 환경 설정 확인
await window.pusherBeamsEnv.check();

// Pusher Beams 상태 확인
await window.pusherBeamsDebug.logStatus();
```

### 권한 확인 절차
1. **플랫폼 지원 여부** 확인
2. **Tauri 알림 플러그인** 사용 가능 여부
3. **원격 푸시 플러그인** 사용 가능 여부 (안드로이드)
4. **웹뷰 기능** 지원 여부
5. **스토어 기능** 사용 가능 여부

## 결론

### ✅ 권한 설정 상태: **완료**
- 모든 필요한 Tauri 권한이 올바르게 설정됨
- 안드로이드 매니페스트 권한 적절히 구성됨
- Firebase/FCM 설정 완료

### 🔧 실제 문제: **초기화 및 환경 설정**
- 권한 부족이 아닌 서비스 초기화 순서 문제
- 개발 환경에서의 브라우저 보안 제약
- Pusher Beams SDK 사용법 관련 문제

### 📋 다음 단계
1. ✅ 권한 설정 완료
2. ✅ 초기화 순서 문제 해결 (이전 작업에서 완료)
3. ✅ 개발 환경 시뮬레이션 모드 구현 (이전 작업에서 완료)
4. 🔄 프로덕션 환경에서 실제 테스트 필요

**권한 관련 문제는 해결되었으며, 현재 푸시 알림 서비스가 정상적으로 작동할 수 있는 모든 권한이 설정되어 있습니다.**

### 선택적 개선
1. **추가 알림 권한** 설정 (더 세밀한 제어)
2. **웹뷰 개발자 도구** 권한 (디버깅 편의성)

## 결론

**현재 권한 설정은 푸시 알림 기능에 충분합니다.** 발생 중인 문제는 권한 부족이 아닌 다음 요인들 때문입니다:

1. ✅ **권한 설정**: 모든 필요 권한이 올바르게 설정됨
2. ⚠️ **환경 설정**: Firebase Sender ID 불일치
3. ⚠️ **개발 환경**: HTTP 프로토콜 제약사항
4. ✅ **초기화 로직**: 이미 개선됨 (시뮬레이션 모드)

**다음 단계**: 환경 변수 통일 후 테스트 진행
